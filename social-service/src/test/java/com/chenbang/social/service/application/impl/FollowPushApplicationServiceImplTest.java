package com.chenbang.social.service.application.impl;

import com.chenbang.social.api.enums.ContentTypeEnum;
import com.chenbang.social.service.application.FeedApplicationService;
import com.chenbang.social.service.application.MessagePushApplicationService;
import com.chenbang.social.service.application.MonitoringApplicationService;
import com.chenbang.social.service.application.assembler.FollowPushConfigAssembler;
import com.chenbang.social.service.domain.entity.ContentPublishTask;
import com.chenbang.social.service.domain.service.FollowPushDomainService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Executor;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

/**
 * FollowPushApplicationServiceImpl 测试类
 * <AUTHOR>
 * @since 2024-07-20
 */
@ExtendWith(MockitoExtension.class)
class FollowPushApplicationServiceImplTest {

    @Mock
    private FollowPushDomainService followPushDomainService;
    
    @Mock
    private FeedApplicationService feedApplicationService;
    
    @Mock
    private MessagePushApplicationService messagePushApplicationService;
    
    @Mock
    private MonitoringApplicationService monitoringApplicationService;
    
    @Mock
    private FollowPushConfigAssembler followPushConfigAssembler;
    
    @Mock
    private Executor taskExecutor;

    @InjectMocks
    private FollowPushApplicationServiceImpl followPushApplicationService;

    private static final Long PUBLISHER_ID = 1001L;
    private static final Long CONTENT_ID = 2001L;
    private static final ContentTypeEnum CONTENT_TYPE = ContentTypeEnum.ARTICLE;
    private static final String CONTENT_TITLE = "测试文章标题";
    private static final String CONTENT_SUMMARY = "测试文章摘要";
    private static final String CONTENT_COVER = "http://example.com/cover.jpg";
    private static final String CONTENT_URL = "http://example.com/article/2001";

    @BeforeEach
    void setUp() {
        // 设置默认的mock行为 - 使用 lenient 避免不必要的 stubbing 警告
        lenient().when(followPushDomainService.getTask(anyString())).thenReturn(null);
        lenient().doNothing().when(followPushDomainService).createProcessTask(anyString(), anyLong(), anyLong(),
                any(ContentTypeEnum.class), anyString(), anyString(), anyString(), anyString());

        // 设置线程池为同步执行，避免异步执行导致的测试问题
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run(); // 同步执行
            return null;
        }).when(taskExecutor).execute(any(Runnable.class));
    }

    @Test
    void testHandleContentPublish_Success() {
        // Given
        String taskId = "content_publish_1001_2001";

        // When
        followPushApplicationService.handleContentPublish(
                PUBLISHER_ID, CONTENT_ID, CONTENT_TYPE,
                CONTENT_TITLE, CONTENT_SUMMARY, CONTENT_COVER, CONTENT_URL);

        // Then
        verify(followPushDomainService).getTask(taskId);
        verify(followPushDomainService).createProcessTask(
                eq(taskId), eq(PUBLISHER_ID), eq(CONTENT_ID), eq(CONTENT_TYPE),
                eq(CONTENT_TITLE), eq(CONTENT_SUMMARY), eq(CONTENT_COVER), eq(CONTENT_URL));
    }

    @Test
    void testHandleContentPublish_TaskAlreadyProcessed() {
        // Given
        String taskId = "content_publish_1001_2001";
        ContentPublishTask existingTask = mock(ContentPublishTask.class);
        when(existingTask.isProcessed()).thenReturn(true);
        when(followPushDomainService.getTask(taskId)).thenReturn(existingTask);

        // When
        followPushApplicationService.handleContentPublish(
                PUBLISHER_ID, CONTENT_ID, CONTENT_TYPE,
                CONTENT_TITLE, CONTENT_SUMMARY, CONTENT_COVER, CONTENT_URL);

        // Then
        verify(followPushDomainService).getTask(taskId);
        verify(followPushDomainService, never()).createProcessTask(anyString(), anyLong(), anyLong(),
                any(ContentTypeEnum.class), anyString(), anyString(), anyString(), anyString());
    }

    @Test
    void testHandleContentPublish_TaskCanRecover() {
        // Given
        String taskId = "content_publish_1001_2001";
        ContentPublishTask existingTask = mock(ContentPublishTask.class);
        when(existingTask.isProcessed()).thenReturn(false);
        when(existingTask.canRecover()).thenReturn(true);
        when(followPushDomainService.getTask(taskId)).thenReturn(existingTask);

        // When
        followPushApplicationService.handleContentPublish(
                PUBLISHER_ID, CONTENT_ID, CONTENT_TYPE,
                CONTENT_TITLE, CONTENT_SUMMARY, CONTENT_COVER, CONTENT_URL);

        // Then
        verify(followPushDomainService).getTask(taskId);
        verify(followPushDomainService, never()).createProcessTask(anyString(), anyLong(), anyLong(),
                any(ContentTypeEnum.class), anyString(), anyString(), anyString(), anyString());
    }

    @Test
    void testHandleContentPublish_TaskCancelled() {
        // Given
        String taskId = "content_publish_1001_2001";
        ContentPublishTask existingTask = mock(ContentPublishTask.class);
        when(existingTask.isProcessed()).thenReturn(false);
        when(existingTask.canRecover()).thenReturn(false);
        when(followPushDomainService.getTask(taskId)).thenReturn(existingTask);

        // When
        followPushApplicationService.handleContentPublish(
                PUBLISHER_ID, CONTENT_ID, CONTENT_TYPE,
                CONTENT_TITLE, CONTENT_SUMMARY, CONTENT_COVER, CONTENT_URL);

        // Then
        verify(followPushDomainService).getTask(taskId);
        verify(followPushDomainService, never()).createProcessTask(anyString(), anyLong(), anyLong(),
                any(ContentTypeEnum.class), anyString(), anyString(), anyString(), anyString());
    }

    @Test
    void testHandleContentPublish_WithNullPublisherId() {
        // When & Then
        try {
            followPushApplicationService.handleContentPublish(
                    null, CONTENT_ID, CONTENT_TYPE, 
                    CONTENT_TITLE, CONTENT_SUMMARY, CONTENT_COVER, CONTENT_URL);
        } catch (IllegalArgumentException e) {
            // Expected exception
        }

        verify(followPushDomainService, never()).getTask(anyString());
    }

    @Test
    void testHandleContentPublish_WithNullContentId() {
        // When & Then
        try {
            followPushApplicationService.handleContentPublish(
                    PUBLISHER_ID, null, CONTENT_TYPE, 
                    CONTENT_TITLE, CONTENT_SUMMARY, CONTENT_COVER, CONTENT_URL);
        } catch (IllegalArgumentException e) {
            // Expected exception
        }

        verify(followPushDomainService, never()).getTask(anyString());
    }

    @Test
    void testHandleContentPublish_CreateTaskFailed() {
        // Given
        String taskId = "content_publish_1001_2001";
        doThrow(new RuntimeException("Database error")).when(followPushDomainService)
                .createProcessTask(anyString(), anyLong(), anyLong(),
                        any(ContentTypeEnum.class), anyString(), anyString(), anyString(), anyString());

        // When & Then
        try {
            followPushApplicationService.handleContentPublish(
                    PUBLISHER_ID, CONTENT_ID, CONTENT_TYPE,
                    CONTENT_TITLE, CONTENT_SUMMARY, CONTENT_COVER, CONTENT_URL);
        } catch (RuntimeException e) {
            // Expected exception
        }

        verify(followPushDomainService).markTaskFailed(eq(taskId), anyString());
    }
}
