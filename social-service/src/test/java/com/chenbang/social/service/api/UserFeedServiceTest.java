package com.chenbang.social.service.api;

import com.chenbang.social.api.dto.request.AddUserFeedRequest;
import com.chenbang.social.api.dto.request.BatchAddUserFeedRequest;
import com.chenbang.social.api.dto.request.CleanExpiredFeedRequest;
import com.chenbang.social.api.dto.request.UserFeedQueryRequest;
import com.chenbang.social.api.dto.response.UserFeedResponse;
import com.chenbang.social.api.enums.ContentTypeEnum;
import com.chenbang.social.api.service.UserFeedService;
import com.chenbang.social.service.application.FeedApplicationService;
import com.chenbang.social.service.application.impl.UserFeedServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * UserFeedService测试类
 * <AUTHOR>
 * @since 2024-07-14
 */
@ExtendWith(MockitoExtension.class)
public class UserFeedServiceTest {

    @Mock
    private FeedApplicationService feedApplicationService;

    @InjectMocks
    private UserFeedServiceImpl userFeedService;

    @Test
    public void testAddUserFeed() {
        // 测试添加用户Feed流记录
        AddUserFeedRequest request = new AddUserFeedRequest();
        request.setUserId(1L);
        request.setPublisherId(2L);
        request.setContentId(3L);
        request.setContentType(ContentTypeEnum.MOMENT);
        request.setContentTitle("测试动态");
        request.setContentSummary("这是一个测试动态");
        request.setContentCover("http://example.com/cover.jpg");
        request.setContentUrl("http://example.com/content");

        // 应该不抛出异常
        assertDoesNotThrow(() -> userFeedService.addUserFeed(request));

        // 验证调用了底层服务
        verify(feedApplicationService).addUserFeed(
                eq(1L), eq(2L), eq(3L), eq(ContentTypeEnum.MOMENT),
                eq("测试动态"), eq("这是一个测试动态"),
                eq("http://example.com/cover.jpg"), eq("http://example.com/content")
        );
    }

    @Test
    public void testBatchAddUserFeed() {
        // 测试批量添加用户Feed流记录
        BatchAddUserFeedRequest request = new BatchAddUserFeedRequest();
        request.setUserIds(Arrays.asList(1L, 2L, 3L));
        request.setPublisherId(4L);
        request.setContentId(5L);
        request.setContentType(ContentTypeEnum.ARTICLE);
        request.setContentTitle("测试文章");
        request.setContentSummary("这是一个测试文章");
        request.setContentCover("http://example.com/article-cover.jpg");
        request.setContentUrl("http://example.com/article");

        // 应该不抛出异常
        assertDoesNotThrow(() -> userFeedService.batchAddUserFeed(request));
    }

    @Test
    public void testQueryUserFeed() {
        // 测试查询用户Feed流
        UserFeedQueryRequest request = new UserFeedQueryRequest();
        request.setUserId(1L);
        request.setPageNum(1);
        request.setPageSize(10);
        request.setContentType(ContentTypeEnum.MOMENT);

        // Mock返回值
        List<UserFeedResponse> mockResponses = Arrays.asList(new UserFeedResponse());
        when(feedApplicationService.queryUserFeed(request)).thenReturn(mockResponses);

        // 执行测试
        List<UserFeedResponse> responses = userFeedService.queryUserFeed(request);

        // 验证结果
        assertNotNull(responses);
        assertEquals(1, responses.size());
        verify(feedApplicationService).queryUserFeed(request);
    }

    @Test
    public void testCleanExpiredFeed() {
        // 测试清理过期Feed流记录
        CleanExpiredFeedRequest request = new CleanExpiredFeedRequest();
        request.setDays(30);

        // 应该不抛出异常
        assertDoesNotThrow(() -> userFeedService.cleanExpiredFeed(request));

        // 验证调用了底层服务
        verify(feedApplicationService).cleanExpiredFeed(30);
    }
}
