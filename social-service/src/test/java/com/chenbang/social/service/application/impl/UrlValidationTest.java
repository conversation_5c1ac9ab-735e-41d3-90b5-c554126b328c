package com.chenbang.social.service.application.impl;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class UrlValidationTest {

    @Test
    void testJavaScriptUrlValidation() {
        String javascriptUrl = "javascript:alert('xss')";
        
        // 测试 Java URL 类是否认为这是有效的 URL
        try {
            java.net.URL url = new java.net.URL(javascriptUrl);
            System.out.println("URL is valid: " + url);
            System.out.println("Protocol: " + url.getProtocol());
        } catch (java.net.MalformedURLException e) {
            System.out.println("URL is invalid: " + e.getMessage());
        }
        
        // 测试我们的安全检查
        String lowerUrl = javascriptUrl.toLowerCase().trim();
        boolean isUnsafe = lowerUrl.startsWith("javascript:");
        System.out.println("Is unsafe: " + isUnsafe);
        
        assertTrue(isUnsafe, "JavaScript URL should be detected as unsafe");
    }
}
