package com.chenbang.social.service.application;

import com.chenbang.social.api.enums.ContentTypeEnum;
import com.chenbang.social.api.enums.TaskStatusEnum;
import com.chenbang.social.service.application.dto.TaskStatistics;
import com.chenbang.social.service.application.impl.FollowPushApplicationServiceImpl;
import com.chenbang.social.service.domain.entity.ContentPublishTask;
import com.chenbang.social.service.domain.service.FollowPushDomainService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.concurrent.Executor;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 关注推送优化测试
 * <AUTHOR>
 * @since 2024-07-15
 */
@ExtendWith(MockitoExtension.class)
class FollowPushOptimizationTest {
    
    @Mock
    private FollowPushDomainService followPushDomainService;
    
    @Mock
    private FeedApplicationService feedApplicationService;
    
    @Mock
    private MessagePushApplicationService messagePushApplicationService;
    
    @Mock
    private TaskManagementApplicationService taskManagementApplicationService;
    
    @Mock
    private TaskMonitoringApplicationService taskMonitoringApplicationService;

    @Mock
    private Executor taskExecutor;

    @InjectMocks
    private FollowPushApplicationServiceImpl followPushApplicationService;
    
    @BeforeEach
    void setUp() {
        // 使用lenient模式避免UnnecessaryStubbing异常
        lenient().when(followPushDomainService.getTask(anyString())).thenReturn(null);

        // 模拟taskExecutor同步执行
        lenient().doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run(); // 同步执行
            return null;
        }).when(taskExecutor).execute(any(Runnable.class));
    }
    
    @Test
    void testHandleContentPublishWithIdempotency() {
        // Given
        Long publisherId = 123L;
        Long contentId = 456L;
        ContentTypeEnum contentType = ContentTypeEnum.MOMENT;
        String contentTitle = "测试内容";
        String contentSummary = "测试摘要";
        String contentCover = "http://example.com/cover.jpg";
        String contentUrl = "http://example.com/content";
        
        // 第一次调用
        followPushApplicationService.handleContentPublish(
                publisherId, contentId, contentType, contentTitle, contentSummary, contentCover, contentUrl
        );
        
        // 验证创建了任务
        verify(followPushDomainService, times(1)).createProcessTask(
                anyString(), eq(publisherId), eq(contentId), eq(contentType),
                eq(contentTitle), eq(contentSummary), eq(contentCover), eq(contentUrl)
        );
        
        // 模拟任务已处理过
        ContentPublishTask completedTask = new ContentPublishTask();
        completedTask.setTaskId("completed_task");
        completedTask.setTaskStatus(TaskStatusEnum.COMPLETED);
        when(followPushDomainService.getTask(anyString())).thenReturn(completedTask);
        
        // 第二次调用（重复处理）
        followPushApplicationService.handleContentPublish(
                publisherId, contentId, contentType, contentTitle, contentSummary, contentCover, contentUrl
        );
        
        // 验证没有重复创建任务
        verify(followPushDomainService, times(1)).createProcessTask(
                anyString(), any(), any(), any(), any(), any(), any(), any()
        );
    }
    
    @Test
    void testTaskManagement() {
        // Given
        String taskId = "test_task_123";
        ContentPublishTask task = new ContentPublishTask();
        task.setTaskId(taskId);
        task.setTaskStatus(TaskStatusEnum.PROCESSING);
        
        when(taskManagementApplicationService.getTaskDetail(taskId)).thenReturn(task);
        when(taskManagementApplicationService.cancelTask(taskId)).thenReturn(true);
        
        // When
        ContentPublishTask result = taskManagementApplicationService.getTaskDetail(taskId);
        boolean cancelled = taskManagementApplicationService.cancelTask(taskId);
        
        // Then
        assertNotNull(result);
        assertEquals(taskId, result.getTaskId());
        assertTrue(cancelled);
        
        verify(taskManagementApplicationService).getTaskDetail(taskId);
        verify(taskManagementApplicationService).cancelTask(taskId);
    }
    
    @Test
    void testTaskMonitoring() {
        // Given
        TaskStatistics mockStats = new TaskStatistics();
        mockStats.setTotalTasks(100L);
        mockStats.setCompletedTasks(80L);
        mockStats.setFailedTasks(5L);
        mockStats.setProcessingTasks(10L);
        mockStats.setPendingTasks(5L);
        
        when(taskMonitoringApplicationService.getTaskStatistics()).thenReturn(mockStats);
        when(taskMonitoringApplicationService.isSystemHealthy()).thenReturn(true);
        when(taskMonitoringApplicationService.getProcessingTaskCount()).thenReturn(10);
        when(taskMonitoringApplicationService.getQueuedTaskCount()).thenReturn(5);
        
        // When
        TaskStatistics stats = taskMonitoringApplicationService.getTaskStatistics();
        boolean isHealthy = taskMonitoringApplicationService.isSystemHealthy();
        int processingCount = taskMonitoringApplicationService.getProcessingTaskCount();
        int queuedCount = taskMonitoringApplicationService.getQueuedTaskCount();
        
        // Then
        assertNotNull(stats);
        assertEquals(100L, stats.getTotalTasks());
        assertEquals(80.0, stats.getSuccessRate(), 0.01);
        assertEquals(5.0, stats.getFailureRate(), 0.01);
        assertTrue(isHealthy);
        assertEquals(10, processingCount);
        assertEquals(5, queuedCount);
    }
    
    @Test
    void testBatchProcessingSimulation() throws InterruptedException {
        // 这个测试模拟大量粉丝的分批处理场景
        
        // Given
        Long publisherId = 999L; // 模拟大V用户
        Long contentId = 888L;
        
        // 模拟100万粉丝的场景
        // 实际测试中我们只模拟小批量数据
        
        // When
        followPushApplicationService.handleContentPublish(
                publisherId, contentId, ContentTypeEnum.MOMENT,
                "大V发布的内容", "这是一个有100万粉丝的大V发布的内容",
                "http://example.com/cover.jpg", "http://example.com/content"
        );
        
        // 等待异步处理完成（实际场景中会有更复杂的等待机制）
        TimeUnit.MILLISECONDS.sleep(500);
        
        // Then
        // 验证任务创建
        verify(followPushDomainService).createProcessTask(
                anyString(), eq(publisherId), eq(contentId), any(),
                anyString(), anyString(), anyString(), anyString()
        );
        
        // 在实际实现中，这里会验证分批处理的调用
        // 由于我们使用了异步处理，这里只能验证任务创建
    }
    
    @Test
    void testErrorHandling() {
        // Given
        Long publisherId = 123L;
        Long contentId = 456L;
        
        // 模拟处理过程中出现异常
        doThrow(new RuntimeException("模拟处理异常"))
                .when(followPushDomainService).createProcessTask(anyString(), any(), any(), any(), any(), any(), any(), any());
        
        // When & Then
        assertThrows(RuntimeException.class, () -> {
            followPushApplicationService.handleContentPublish(
                    publisherId, contentId, ContentTypeEnum.MOMENT,
                    "测试内容", "测试摘要", "http://example.com/cover.jpg", "http://example.com/content"
            );
        });
        
        // 验证异常处理
        verify(followPushDomainService).markTaskFailed(anyString(), anyString());
    }
}
