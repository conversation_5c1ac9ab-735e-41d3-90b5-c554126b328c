package com.chenbang.social.service.application.impl;

import com.chenbang.social.service.domain.entity.ContentPublishTask;
import com.chenbang.social.service.domain.entity.TaskBatchError;
import com.chenbang.social.service.domain.repository.ContentPublishTaskRepository;
import com.chenbang.social.service.domain.repository.TaskBatchErrorRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * TaskManagementApplicationServiceImpl 测试类
 * <AUTHOR>
 * @since 2024-07-20
 */
@ExtendWith(MockitoExtension.class)
class TaskManagementApplicationServiceImplTest {

    @Mock
    private ContentPublishTaskRepository contentPublishTaskRepository;
    
    @Mock
    private TaskBatchErrorRepository taskBatchErrorRepository;

    @InjectMocks
    private TaskManagementApplicationServiceImpl taskManagementApplicationService;

    private TaskBatchError mockError;
    private ContentPublishTask mockTask;

    @BeforeEach
    void setUp() {
        // 创建模拟的批次错误
        mockError = new TaskBatchError();
        mockError.setId(1L);
        mockError.setTaskId("task_1001_2001");
        mockError.setPushType("IN_APP_MESSAGE");
        mockError.setBatchOffset(0);
        mockError.setErrorMessage("Network timeout");
        mockError.setRetryCount(0);
        mockError.setIsFixed(false);
        mockError.setCreateTime(LocalDateTime.now());

        // 创建模拟的任务
        mockTask = new ContentPublishTask();
        mockTask.setTaskId("task_1001_2001");
        mockTask.setPublisherId(1001L);
        mockTask.setContentId(2001L);
    }

    @Test
    void testRetryBatchError_Success() {
        // Given
        when(taskBatchErrorRepository.findById(1L)).thenReturn(mockError);
        when(contentPublishTaskRepository.findByTaskId("task_1001_2001")).thenReturn(mockTask);

        // When
        boolean result = taskManagementApplicationService.retryBatchError(1L);

        // Then
        assertTrue(result);
        verify(taskBatchErrorRepository, times(2)).update(mockError); // 一次增加重试次数，一次标记为已修复
        assertEquals(1, mockError.getRetryCount());
        assertTrue(mockError.getIsFixed());
    }

    @Test
    void testRetryBatchError_ErrorNotFound() {
        // Given
        when(taskBatchErrorRepository.findById(1L)).thenReturn(null);

        // When
        boolean result = taskManagementApplicationService.retryBatchError(1L);

        // Then
        assertFalse(result);
        verify(taskBatchErrorRepository, never()).update(any());
    }

    @Test
    void testRetryBatchError_CannotRetry() {
        // Given
        mockError.setRetryCount(3); // 已达到最大重试次数
        when(taskBatchErrorRepository.findById(1L)).thenReturn(mockError);

        // When
        boolean result = taskManagementApplicationService.retryBatchError(1L);

        // Then
        assertFalse(result);
        verify(taskBatchErrorRepository, never()).update(any());
    }

    @Test
    void testRetryBatchError_AlreadyFixed() {
        // Given
        mockError.setIsFixed(true);
        when(taskBatchErrorRepository.findById(1L)).thenReturn(mockError);

        // When
        boolean result = taskManagementApplicationService.retryBatchError(1L);

        // Then
        assertFalse(result);
        verify(taskBatchErrorRepository, never()).update(any());
    }

    @Test
    void testRetryBatchError_TaskNotFound() {
        // Given
        when(taskBatchErrorRepository.findById(1L)).thenReturn(mockError);
        when(contentPublishTaskRepository.findByTaskId("task_1001_2001")).thenReturn(null);

        // When
        boolean result = taskManagementApplicationService.retryBatchError(1L);

        // Then
        assertFalse(result);
        verify(taskBatchErrorRepository, times(1)).update(mockError); // 只增加重试次数，不标记为已修复
        assertEquals(1, mockError.getRetryCount());
        assertFalse(mockError.getIsFixed());
    }

    @Test
    void testRetryBatchError_Exception() {
        // Given
        when(taskBatchErrorRepository.findById(1L)).thenReturn(mockError);
        doThrow(new RuntimeException("Database error")).when(taskBatchErrorRepository).update(mockError);

        // When
        boolean result = taskManagementApplicationService.retryBatchError(1L);

        // Then
        assertFalse(result);
    }

    @Test
    void testListTaskBatchErrors() {
        // Given
        String taskId = "task_1001_2001";
        List<TaskBatchError> expectedErrors = Arrays.asList(mockError);
        when(taskBatchErrorRepository.findByTaskId(taskId)).thenReturn(expectedErrors);

        // When
        List<TaskBatchError> result = taskManagementApplicationService.listTaskBatchErrors(taskId);

        // Then
        assertEquals(expectedErrors, result);
        verify(taskBatchErrorRepository).findByTaskId(taskId);
    }

    @Test
    void testCleanExpiredTasks() {
        // Given
        int days = 7;
        LocalDateTime expireTime = LocalDateTime.now().minusDays(days);
        when(contentPublishTaskRepository.deleteExpiredTasks(any(LocalDateTime.class))).thenReturn(5);
        when(taskBatchErrorRepository.deleteExpiredErrors(any(LocalDateTime.class))).thenReturn(3);

        // When
        int result = taskManagementApplicationService.cleanExpiredTasks(days);

        // Then
        assertEquals(5, result);
        verify(contentPublishTaskRepository).deleteExpiredTasks(any(LocalDateTime.class));
        verify(taskBatchErrorRepository).deleteExpiredErrors(any(LocalDateTime.class));
    }
}
