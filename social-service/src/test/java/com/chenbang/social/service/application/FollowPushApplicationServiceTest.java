package com.chenbang.social.service.application;

import com.chenbang.social.api.dto.request.FollowPushConfigRequest;
import com.chenbang.social.api.dto.response.FollowPushConfigResponse;
import com.chenbang.social.api.enums.ContentTypeEnum;
import com.chenbang.social.api.enums.PushTypeEnum;
import com.chenbang.social.service.application.FeedApplicationService;
import com.chenbang.social.service.application.MessagePushApplicationService;
import com.chenbang.social.service.application.assembler.FollowPushConfigAssembler;
import com.chenbang.social.service.application.impl.FollowPushApplicationServiceImpl;
import com.chenbang.social.service.domain.entity.FollowPushConfig;
import com.chenbang.social.service.domain.service.FollowPushDomainService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 关注推送应用服务测试
 * <AUTHOR>
 * @since 2024-07-14
 */
@ExtendWith(MockitoExtension.class)
class FollowPushApplicationServiceTest {
    
    @Mock
    private FollowPushDomainService followPushDomainService;

    @Mock
    private FeedApplicationService feedApplicationService;

    @Mock
    private MessagePushApplicationService messagePushApplicationService;

    @Mock
    private FollowPushConfigAssembler followPushConfigAssembler;

    @InjectMocks
    private FollowPushApplicationServiceImpl followPushApplicationService;
    
    private FollowPushConfigRequest configRequest;
    private FollowPushConfig followPushConfig;
    
    @BeforeEach
    void setUp() {
        configRequest = new FollowPushConfigRequest();
        configRequest.setUserId(123L);
        configRequest.setPushType(PushTypeEnum.BOTH);
        configRequest.setEnabled(true);

        followPushConfig = FollowPushConfig.create(123L, PushTypeEnum.BOTH, true, "default", "system");
        followPushConfig.setId(1L);
    }
    
    @Test
    void testConfigFollowPush() {
        // Given
        FollowPushConfigResponse expectedResponse = new FollowPushConfigResponse();
        expectedResponse.setUserId(123L);
        expectedResponse.setPushType(PushTypeEnum.BOTH);
        expectedResponse.setEnabled(true);

        when(followPushDomainService.createOrUpdateConfig(anyLong(), any(PushTypeEnum.class), anyBoolean(), anyString(), anyString()))
                .thenReturn(followPushConfig);
        when(followPushConfigAssembler.toResponse(followPushConfig)).thenReturn(expectedResponse);

        // When
        FollowPushConfigResponse response = followPushApplicationService.configFollowPush(configRequest);

        // Then
        assertNotNull(response);
        assertEquals(123L, response.getUserId());
        assertEquals(PushTypeEnum.BOTH, response.getPushType());
        assertTrue(response.getEnabled());

        verify(followPushDomainService).createOrUpdateConfig(123L, PushTypeEnum.BOTH, true, "default", "system");
        verify(followPushConfigAssembler).toResponse(followPushConfig);
    }
    
    @Test
    void testGetFollowPushConfig() {
        // Given
        FollowPushConfigResponse expectedResponse = new FollowPushConfigResponse();
        expectedResponse.setUserId(123L);
        expectedResponse.setPushType(PushTypeEnum.BOTH);
        expectedResponse.setEnabled(true);

        when(followPushDomainService.getConfig(123L)).thenReturn(followPushConfig);
        when(followPushConfigAssembler.toResponse(followPushConfig)).thenReturn(expectedResponse);

        // When
        FollowPushConfigResponse response = followPushApplicationService.getFollowPushConfig(123L);

        // Then
        assertNotNull(response);
        assertEquals(123L, response.getUserId());
        assertEquals(PushTypeEnum.BOTH, response.getPushType());
        assertTrue(response.getEnabled());

        verify(followPushDomainService).getConfig(123L);
        verify(followPushConfigAssembler).toResponse(followPushConfig);
    }
    
    @Test
    void testHandleContentPublish() {
        // Given
        Long publisherId = 456L;
        Long contentId = 789L;
        ContentTypeEnum contentType = ContentTypeEnum.MOMENT;
        String contentTitle = "测试内容";
        String contentSummary = "测试摘要";
        String contentCover = "http://example.com/cover.jpg";
        String contentUrl = "http://example.com/content";

        // Mock任务不存在，需要创建新任务
        when(followPushDomainService.getTask(anyString())).thenReturn(null);

        // When
        followPushApplicationService.handleContentPublish(
                publisherId, contentId, contentType, contentTitle, contentSummary, contentCover, contentUrl
        );

        // Then
        verify(followPushDomainService, atLeastOnce()).getTask(anyString());
        verify(followPushDomainService).createProcessTask(anyString(), eq(publisherId), eq(contentId),
                eq(contentType), eq(contentTitle), eq(contentSummary), eq(contentCover), eq(contentUrl));
    }
}
