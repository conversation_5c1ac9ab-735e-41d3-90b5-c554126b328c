package com.chenbang.social.service.api;

import com.chenbang.social.api.dto.request.QueryPushMessageRecordRequest;
import com.chenbang.social.api.dto.response.PushMessageRecordResponse;
import com.chenbang.social.api.service.PushMessageService;
import com.chenbang.social.service.application.MessagePushApplicationService;
import com.chenbang.social.service.application.impl.PushMessageServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * PushMessageService测试类
 * <AUTHOR>
 * @since 2024-07-14
 */
@ExtendWith(MockitoExtension.class)
public class PushMessageServiceTest {

    @Mock
    private MessagePushApplicationService messagePushApplicationService;
    
    @InjectMocks
    private PushMessageServiceImpl pushMessageService;

    @Test
    public void testQueryPushMessageRecord() {
        // 测试查询推送消息记录
        QueryPushMessageRecordRequest request = new QueryPushMessageRecordRequest();
        request.setUserId(1L);
        request.setPageNum(1);
        request.setPageSize(20);

        // Mock返回值
        List<PushMessageRecordResponse> mockResponses = Arrays.asList(new PushMessageRecordResponse());
        when(messagePushApplicationService.queryPushMessageRecord(request)).thenReturn(mockResponses);

        // 执行测试
        List<PushMessageRecordResponse> responses = pushMessageService.queryPushMessageRecord(request);
        
        // 验证结果
        assertNotNull(responses);
        assertEquals(1, responses.size());
        verify(messagePushApplicationService).queryPushMessageRecord(request);
    }
}
