package com.chenbang.social.service.application.impl;

import com.chenbang.social.api.enums.ContentTypeEnum;
import com.chenbang.social.service.domain.service.FollowPushDomainService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.lang.reflect.Field;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * FollowPushApplicationServiceImpl 并发控制测试
 */
@ExtendWith(MockitoExtension.class)
class FollowPushApplicationServiceImplConcurrencyTest {

    @Mock
    private FollowPushDomainService followPushDomainService;

    @Mock
    private TaskExecutor taskExecutor;

    @Mock
    private StringRedisTemplate stringRedisTemplate;

    @Mock
    private ValueOperations<String, String> valueOperations;

    @InjectMocks
    private FollowPushApplicationServiceImpl followPushApplicationService;

    private static final Long PUBLISHER_ID = 1001L;
    private static final Long CONTENT_ID = 2001L;
    private static final ContentTypeEnum CONTENT_TYPE = ContentTypeEnum.ARTICLE;

    @BeforeEach
    void setUp() {
        when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);
        when(valueOperations.setIfAbsent(anyString(), anyString(), any(java.time.Duration.class)))
                .thenReturn(true);
        when(followPushDomainService.getTask(anyString())).thenReturn(null);
    }

    @Test
    void testConcurrentTaskLimit_ShouldRejectWhenExceedsLimit() throws Exception {
        // Given - 设置当前任务数为最大值
        setCurrentTaskCount(100); // MAX_CONCURRENT_TASKS = 100

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            followPushApplicationService.handleContentPublish(
                    PUBLISHER_ID, CONTENT_ID, CONTENT_TYPE,
                    "标题", "摘要", "http://example.com/cover.jpg", "http://example.com/content");
        });

        assertTrue(exception.getMessage().contains("当前并发任务数已达上限"));
        
        // 验证计数器被正确回退
        assertEquals(100, getCurrentTaskCount());
    }

    @Test
    void testConcurrentTaskLimit_ShouldAcceptWhenWithinLimit() throws Exception {
        // Given - 设置当前任务数为最大值-1
        setCurrentTaskCount(99);

        // When & Then - 不应该抛出异常
        assertDoesNotThrow(() -> {
            followPushApplicationService.handleContentPublish(
                    PUBLISHER_ID, CONTENT_ID, CONTENT_TYPE,
                    "标题", "摘要", "http://example.com/cover.jpg", "http://example.com/content");
        });

        // 验证任务创建被调用
        verify(followPushDomainService).createProcessTask(anyString(), anyLong(), anyLong(),
                any(ContentTypeEnum.class), anyString(), anyString(), anyString(), anyString());
    }

    @Test
    void testConcurrentAccess_ShouldHandleRaceCondition() throws Exception {
        // Given
        int threadCount = 10;
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch endLatch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);

        // 设置当前任务数为接近限制
        setCurrentTaskCount(95);

        // When
        for (int i = 0; i < threadCount; i++) {
            final int taskIndex = i;
            executor.submit(() -> {
                try {
                    startLatch.await(); // 等待所有线程准备就绪
                    
                    followPushApplicationService.handleContentPublish(
                            PUBLISHER_ID, CONTENT_ID + taskIndex, CONTENT_TYPE,
                            "标题" + taskIndex, "摘要", "http://example.com/cover.jpg", "http://example.com/content");
                    
                    successCount.incrementAndGet();
                } catch (RuntimeException e) {
                    if (e.getMessage().contains("当前并发任务数已达上限")) {
                        failureCount.incrementAndGet();
                    } else {
                        // 其他异常，重新抛出
                        throw e;
                    }
                } catch (Exception e) {
                    // 忽略其他异常（如InterruptedException）
                } finally {
                    endLatch.countDown();
                }
            });
        }

        // 同时启动所有线程
        startLatch.countDown();
        endLatch.await();
        executor.shutdown();

        // Then
        assertTrue(successCount.get() > 0, "应该有一些任务成功");
        assertTrue(failureCount.get() > 0, "应该有一些任务因并发限制失败");
        assertEquals(threadCount, successCount.get() + failureCount.get(), "所有任务都应该有结果");
        
        // 验证计数器不会超过限制
        assertTrue(getCurrentTaskCount() <= 100, "任务计数器不应该超过最大限制");
    }

    @Test
    void testTaskCounterConsistency_WhenTaskExecutorFails() throws Exception {
        // Given
        setCurrentTaskCount(50);
        
        // 模拟线程池执行失败
        doThrow(new RuntimeException("线程池异常")).when(taskExecutor).execute(any(Runnable.class));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            followPushApplicationService.handleContentPublish(
                    PUBLISHER_ID, CONTENT_ID, CONTENT_TYPE,
                    "标题", "摘要", "http://example.com/cover.jpg", "http://example.com/content");
        });

        assertTrue(exception.getMessage().contains("线程池异常"));
        
        // 验证计数器被正确回退
        assertEquals(50, getCurrentTaskCount());
    }

    @Test
    void testTaskCounterConsistency_WhenTaskCreationFails() throws Exception {
        // Given
        setCurrentTaskCount(50);
        
        // 模拟任务创建失败
        doThrow(new RuntimeException("数据库异常")).when(followPushDomainService)
                .createProcessTask(anyString(), anyLong(), anyLong(), any(ContentTypeEnum.class),
                        anyString(), anyString(), anyString(), anyString());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            followPushApplicationService.handleContentPublish(
                    PUBLISHER_ID, CONTENT_ID, CONTENT_TYPE,
                    "标题", "摘要", "http://example.com/cover.jpg", "http://example.com/content");
        });

        assertTrue(exception.getMessage().contains("数据库异常"));
        
        // 验证计数器被正确回退
        assertEquals(50, getCurrentTaskCount());
    }

    @Test
    void testTaskCounterConsistency_WhenLockAcquisitionFails() throws Exception {
        // Given
        setCurrentTaskCount(50);
        
        // 模拟锁获取失败
        when(valueOperations.setIfAbsent(anyString(), anyString(), any(java.time.Duration.class)))
                .thenReturn(false);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            followPushApplicationService.handleContentPublish(
                    PUBLISHER_ID, CONTENT_ID, CONTENT_TYPE,
                    "标题", "摘要", "http://example.com/cover.jpg", "http://example.com/content");
        });

        assertTrue(exception.getMessage().contains("获取处理锁失败"));
        
        // 验证计数器被正确回退
        assertEquals(50, getCurrentTaskCount());
    }

    /**
     * 通过反射设置当前任务计数
     */
    private void setCurrentTaskCount(int count) throws Exception {
        Field field = FollowPushApplicationServiceImpl.class.getDeclaredField("currentTaskCount");
        field.setAccessible(true);
        AtomicInteger counter = (AtomicInteger) field.get(followPushApplicationService);
        counter.set(count);
    }

    /**
     * 通过反射获取当前任务计数
     */
    private int getCurrentTaskCount() throws Exception {
        Field field = FollowPushApplicationServiceImpl.class.getDeclaredField("currentTaskCount");
        field.setAccessible(true);
        AtomicInteger counter = (AtomicInteger) field.get(followPushApplicationService);
        return counter.get();
    }
}
