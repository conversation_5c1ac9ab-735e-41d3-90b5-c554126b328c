package com.chenbang.social.service.application.impl;

import com.chenbang.social.service.domain.entity.TaskBatchError;
import com.chenbang.social.service.domain.repository.TaskBatchErrorRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * MonitoringApplicationServiceImpl 测试类
 * <AUTHOR>
 * @since 2024-07-20
 */
@ExtendWith(MockitoExtension.class)
class MonitoringApplicationServiceImplTest {

    @Mock
    private TaskBatchErrorRepository taskBatchErrorRepository;

    @InjectMocks
    private MonitoringApplicationServiceImpl monitoringApplicationService;

    private static final String TASK_ID = "task_1001_2001";
    private static final String PUSH_TYPE = "IN_APP_MESSAGE";
    private static final int BATCH_OFFSET = 0;
    private static final String ERROR_MESSAGE = "Network timeout";
    private static final int BATCH_SIZE = 100;

    @Test
    void testRecordBatchFailure() {
        // Given
        when(taskBatchErrorRepository.findByTaskId(TASK_ID)).thenReturn(Collections.emptyList());

        // When
        monitoringApplicationService.recordBatchFailure(TASK_ID, PUSH_TYPE, BATCH_OFFSET, ERROR_MESSAGE, BATCH_SIZE);

        // Then
        verify(taskBatchErrorRepository).findByTaskId(TASK_ID);
        // 验证日志记录（这里只能通过观察日志输出来验证）
    }

    @Test
    void testRecordBatchSuccess() {
        // Given
        long processingTimeMs = 1500;

        // When
        monitoringApplicationService.recordBatchSuccess(TASK_ID, PUSH_TYPE, BATCH_OFFSET, BATCH_SIZE, processingTimeMs);

        // Then
        // 验证日志记录（这里只能通过观察日志输出来验证）
        // 正常情况下不应该有警告日志
    }

    @Test
    void testRecordBatchSuccess_SlowProcessing() {
        // Given
        long processingTimeMs = 15000; // 超过10秒的慢处理

        // When
        monitoringApplicationService.recordBatchSuccess(TASK_ID, PUSH_TYPE, BATCH_OFFSET, BATCH_SIZE, processingTimeMs);

        // Then
        // 验证日志记录（这里只能通过观察日志输出来验证）
        // 应该有警告日志关于处理时间过长
    }

    @Test
    void testShouldAlert_BelowThreshold() {
        // Given
        List<TaskBatchError> errors = createMockErrors(3, false); // 3个未修复错误，低于阈值5
        when(taskBatchErrorRepository.findByTaskId(TASK_ID)).thenReturn(errors);

        // When
        boolean result = monitoringApplicationService.shouldAlert(TASK_ID);

        // Then
        assertFalse(result);
        verify(taskBatchErrorRepository).findByTaskId(TASK_ID);
    }

    @Test
    void testShouldAlert_AboveThreshold() {
        // Given
        List<TaskBatchError> errors = createMockErrors(6, false); // 6个未修复错误，超过阈值5
        when(taskBatchErrorRepository.findByTaskId(TASK_ID)).thenReturn(errors);

        // When
        boolean result = monitoringApplicationService.shouldAlert(TASK_ID);

        // Then
        assertTrue(result);
        verify(taskBatchErrorRepository).findByTaskId(TASK_ID);
    }

    @Test
    void testShouldAlert_FixedErrors() {
        // Given
        List<TaskBatchError> errors = createMockErrors(6, true); // 6个已修复错误
        when(taskBatchErrorRepository.findByTaskId(TASK_ID)).thenReturn(errors);

        // When
        boolean result = monitoringApplicationService.shouldAlert(TASK_ID);

        // Then
        assertFalse(result); // 已修复的错误不应该触发告警
        verify(taskBatchErrorRepository).findByTaskId(TASK_ID);
    }

    @Test
    void testShouldAlert_MixedErrors() {
        // Given
        List<TaskBatchError> errors = Arrays.asList(
                createMockError(false), // 未修复
                createMockError(false), // 未修复
                createMockError(false), // 未修复
                createMockError(false), // 未修复
                createMockError(false), // 未修复 - 达到阈值5
                createMockError(true),  // 已修复
                createMockError(true)   // 已修复
        );
        when(taskBatchErrorRepository.findByTaskId(TASK_ID)).thenReturn(errors);

        // When
        boolean result = monitoringApplicationService.shouldAlert(TASK_ID);

        // Then
        assertTrue(result); // 5个未修复错误，达到阈值
        verify(taskBatchErrorRepository).findByTaskId(TASK_ID);
    }

    @Test
    void testShouldAlert_Exception() {
        // Given
        when(taskBatchErrorRepository.findByTaskId(TASK_ID)).thenThrow(new RuntimeException("Database error"));

        // When
        boolean result = monitoringApplicationService.shouldAlert(TASK_ID);

        // Then
        assertFalse(result); // 异常情况下不触发告警
        verify(taskBatchErrorRepository).findByTaskId(TASK_ID);
    }

    @Test
    void testSendAlert() {
        // Given
        String alertMessage = "测试告警消息";

        // When
        monitoringApplicationService.sendAlert(TASK_ID, alertMessage);

        // Then
        // 验证日志记录（这里只能通过观察日志输出来验证）
        // 应该有错误级别的告警日志和信息级别的发送成功日志
    }

    @Test
    void testRecordBatchFailure_WithAlert() {
        // Given
        List<TaskBatchError> errors = createMockErrors(5, false); // 5个未修复错误，达到阈值
        when(taskBatchErrorRepository.findByTaskId(TASK_ID)).thenReturn(errors);

        // When
        monitoringApplicationService.recordBatchFailure(TASK_ID, PUSH_TYPE, BATCH_OFFSET, ERROR_MESSAGE, BATCH_SIZE);

        // Then
        verify(taskBatchErrorRepository).findByTaskId(TASK_ID);
        // 应该触发告警（通过日志验证）
    }

    /**
     * 创建模拟的错误列表
     */
    private List<TaskBatchError> createMockErrors(int count, boolean isFixed) {
        return Arrays.asList(new TaskBatchError[count]).stream()
                .map(ignored -> createMockError(isFixed))
                .toList();
    }

    /**
     * 创建模拟的错误对象
     */
    private TaskBatchError createMockError(boolean isFixed) {
        TaskBatchError error = new TaskBatchError();
        error.setTaskId(TASK_ID);
        error.setPushType(PUSH_TYPE);
        error.setErrorMessage(ERROR_MESSAGE);
        error.setIsFixed(isFixed);
        error.setRetryCount(0);
        error.setCreateTime(LocalDateTime.now());
        return error;
    }
}
