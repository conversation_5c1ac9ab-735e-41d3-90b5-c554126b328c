package com.chenbang.social.service.application.impl;

import com.chenbang.social.api.enums.ContentTypeEnum;
import com.chenbang.social.service.domain.service.FollowPushDomainService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

/**
 * FollowPushApplicationServiceImpl 安全性和参数验证测试
 */
@ExtendWith(MockitoExtension.class)
class FollowPushApplicationServiceImplSecurityTest {

    @Mock
    private FollowPushDomainService followPushDomainService;

    @Mock
    private TaskExecutor taskExecutor;

    @Mock
    private StringRedisTemplate stringRedisTemplate;

    @Mock
    private ValueOperations<String, String> valueOperations;

    @InjectMocks
    private FollowPushApplicationServiceImpl followPushApplicationService;

    private static final Long VALID_PUBLISHER_ID = 1001L;
    private static final Long VALID_CONTENT_ID = 2001L;
    private static final ContentTypeEnum VALID_CONTENT_TYPE = ContentTypeEnum.ARTICLE;

    @BeforeEach
    void setUp() {
        // 只在需要的测试中设置 Mock
        lenient().when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);
        lenient().when(valueOperations.setIfAbsent(anyString(), anyString(), any(java.time.Duration.class)))
                .thenReturn(true);
        lenient().when(followPushDomainService.getTask(anyString())).thenReturn(null);
        lenient().doNothing().when(taskExecutor).execute(any(Runnable.class));
    }

    @Test
    void testHandleContentPublish_NullPublisherId_ShouldThrowException() {
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            followPushApplicationService.handleContentPublish(
                    null, VALID_CONTENT_ID, VALID_CONTENT_TYPE,
                    "标题", "摘要", "http://example.com/cover.jpg", "http://example.com/content");
        });

        assertEquals("publisherId 不能为空且必须大于0", exception.getMessage());
    }

    @Test
    void testHandleContentPublish_ZeroPublisherId_ShouldThrowException() {
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            followPushApplicationService.handleContentPublish(
                    0L, VALID_CONTENT_ID, VALID_CONTENT_TYPE,
                    "标题", "摘要", "http://example.com/cover.jpg", "http://example.com/content");
        });

        assertEquals("publisherId 不能为空且必须大于0", exception.getMessage());
    }

    @Test
    void testHandleContentPublish_NegativePublisherId_ShouldThrowException() {
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            followPushApplicationService.handleContentPublish(
                    -1L, VALID_CONTENT_ID, VALID_CONTENT_TYPE,
                    "标题", "摘要", "http://example.com/cover.jpg", "http://example.com/content");
        });

        assertEquals("publisherId 不能为空且必须大于0", exception.getMessage());
    }

    @Test
    void testHandleContentPublish_NullContentId_ShouldThrowException() {
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            followPushApplicationService.handleContentPublish(
                    VALID_PUBLISHER_ID, null, VALID_CONTENT_TYPE,
                    "标题", "摘要", "http://example.com/cover.jpg", "http://example.com/content");
        });

        assertEquals("contentId 不能为空且必须大于0", exception.getMessage());
    }

    @Test
    void testHandleContentPublish_NullContentType_ShouldThrowException() {
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            followPushApplicationService.handleContentPublish(
                    VALID_PUBLISHER_ID, VALID_CONTENT_ID, null,
                    "标题", "摘要", "http://example.com/cover.jpg", "http://example.com/content");
        });

        assertEquals("contentType 不能为空", exception.getMessage());
    }

    @Test
    void testHandleContentPublish_TooLongTitle_ShouldThrowException() {
        // Given
        String longTitle = "a".repeat(501); // 超过500字符限制

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            followPushApplicationService.handleContentPublish(
                    VALID_PUBLISHER_ID, VALID_CONTENT_ID, VALID_CONTENT_TYPE,
                    longTitle, "摘要", "http://example.com/cover.jpg", "http://example.com/content");
        });

        assertEquals("contentTitle 长度不能超过500字符", exception.getMessage());
    }

    @Test
    void testHandleContentPublish_TooLongSummary_ShouldThrowException() {
        // Given
        String longSummary = "a".repeat(1001); // 超过1000字符限制

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            followPushApplicationService.handleContentPublish(
                    VALID_PUBLISHER_ID, VALID_CONTENT_ID, VALID_CONTENT_TYPE,
                    "标题", longSummary, "http://example.com/cover.jpg", "http://example.com/content");
        });

        assertEquals("contentSummary 长度不能超过1000字符", exception.getMessage());
    }

    @Test
    void testHandleContentPublish_DangerousCharactersInTitle_ShouldThrowException() {
        // Given
        String dangerousTitle = "正常标题<script>alert('xss')</script>";

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            followPushApplicationService.handleContentPublish(
                    VALID_PUBLISHER_ID, VALID_CONTENT_ID, VALID_CONTENT_TYPE,
                    dangerousTitle, "摘要", "http://example.com/cover.jpg", "http://example.com/content");
        });

        assertEquals("contentTitle 包含非法字符", exception.getMessage());
    }

    @Test
    void testHandleContentPublish_SqlInjectionInSummary_ShouldThrowException() {
        // Given
        String sqlInjection = "正常摘要'; DROP TABLE users; --";

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            followPushApplicationService.handleContentPublish(
                    VALID_PUBLISHER_ID, VALID_CONTENT_ID, VALID_CONTENT_TYPE,
                    "标题", sqlInjection, "http://example.com/cover.jpg", "http://example.com/content");
        });

        assertEquals("contentSummary 包含非法字符", exception.getMessage());
    }

    @Test
    void testHandleContentPublish_InvalidUrl_ShouldThrowException() {
        // Given
        String invalidUrl = "not-a-valid-url";

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            followPushApplicationService.handleContentPublish(
                    VALID_PUBLISHER_ID, VALID_CONTENT_ID, VALID_CONTENT_TYPE,
                    "标题", "摘要", invalidUrl, "http://example.com/content");
        });

        assertEquals("contentCover URL格式不正确", exception.getMessage());
    }

    @Test
    void testHandleContentPublish_ValidParameters_ShouldSucceed() {
        // Given - 所有参数都有效

        // When & Then - 不应该抛出异常
        assertDoesNotThrow(() -> {
            followPushApplicationService.handleContentPublish(
                    VALID_PUBLISHER_ID, VALID_CONTENT_ID, VALID_CONTENT_TYPE,
                    "正常标题", "正常摘要", "http://example.com/cover.jpg", "http://example.com/content");
        });

        // 验证任务创建被调用
        verify(followPushDomainService).createProcessTask(
                anyString(), eq(VALID_PUBLISHER_ID), eq(VALID_CONTENT_ID), eq(VALID_CONTENT_TYPE),
                eq("正常标题"), eq("正常摘要"), eq("http://example.com/cover.jpg"), eq("http://example.com/content"));
    }

    @Test
    void testHandleContentPublish_NullOptionalParameters_ShouldSucceed() {
        // Given - 可选参数为null

        // When & Then - 不应该抛出异常
        assertDoesNotThrow(() -> {
            followPushApplicationService.handleContentPublish(
                    VALID_PUBLISHER_ID, VALID_CONTENT_ID, VALID_CONTENT_TYPE,
                    null, null, null, null);
        });

        // 验证任务创建被调用
        verify(followPushDomainService).createProcessTask(
                anyString(), eq(VALID_PUBLISHER_ID), eq(VALID_CONTENT_ID), eq(VALID_CONTENT_TYPE),
                isNull(), isNull(), isNull(), isNull());
    }

    @Test
    void testHandleContentPublish_EmptyStringParameters_ShouldSucceed() {
        // Given - 可选参数为空字符串

        // When & Then - 不应该抛出异常
        assertDoesNotThrow(() -> {
            followPushApplicationService.handleContentPublish(
                    VALID_PUBLISHER_ID, VALID_CONTENT_ID, VALID_CONTENT_TYPE,
                    "", "", "", "");
        });

        // 验证任务创建被调用
        verify(followPushDomainService).createProcessTask(
                anyString(), eq(VALID_PUBLISHER_ID), eq(VALID_CONTENT_ID), eq(VALID_CONTENT_TYPE),
                eq(""), eq(""), eq(""), eq(""));
    }
}
