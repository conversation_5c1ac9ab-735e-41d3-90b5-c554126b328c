package com.chenbang.social.service.application.impl;

import com.chenbang.social.api.enums.ContentTypeEnum;
import com.chenbang.social.api.enums.TaskStatusEnum;
import com.chenbang.social.service.application.FeedApplicationService;
import com.chenbang.social.service.application.MessagePushApplicationService;
import com.chenbang.social.service.application.MonitoringApplicationService;
import com.chenbang.social.service.application.assembler.FollowPushConfigAssembler;
import com.chenbang.social.service.domain.entity.ContentPublishTask;
import com.chenbang.social.service.domain.service.FollowPushDomainService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.concurrent.Executor;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import java.lang.reflect.InvocationTargetException;

/**
 * FollowPushApplicationServiceImpl 修复后的测试
 * 主要测试并发控制、分布式锁、异常处理等修复的问题
 */
@ExtendWith(MockitoExtension.class)
class FollowPushApplicationServiceImplFixTest {

    @Mock
    private FollowPushDomainService followPushDomainService;
    @Mock
    private FeedApplicationService feedApplicationService;
    @Mock
    private MessagePushApplicationService messagePushApplicationService;
    @Mock
    private MonitoringApplicationService monitoringApplicationService;
    @Mock
    private FollowPushConfigAssembler followPushConfigAssembler;
    @Mock
    private StringRedisTemplate stringRedisTemplate;
    @Mock
    private ValueOperations<String, String> valueOperations;
    @Mock
    private Executor taskExecutor;

    private FollowPushApplicationServiceImpl followPushApplicationService;

    private static final Long PUBLISHER_ID = 1001L;
    private static final Long CONTENT_ID = 2001L;
    private static final ContentTypeEnum CONTENT_TYPE = ContentTypeEnum.MOMENT;
    private static final String CONTENT_TITLE = "测试内容标题";
    private static final String CONTENT_SUMMARY = "测试内容摘要";
    private static final String CONTENT_COVER = "http://example.com/cover.jpg";
    private static final String CONTENT_URL = "http://example.com/content";

    @BeforeEach
    void setUp() {
        followPushApplicationService = new FollowPushApplicationServiceImpl(
                followPushDomainService,
                feedApplicationService,
                messagePushApplicationService,
                monitoringApplicationService,
                followPushConfigAssembler,
                stringRedisTemplate
        );

        // 使用反射设置taskExecutor
        try {
            java.lang.reflect.Field taskExecutorField = FollowPushApplicationServiceImpl.class.getDeclaredField("taskExecutor");
            taskExecutorField.setAccessible(true);
            taskExecutorField.set(followPushApplicationService, taskExecutor);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        lenient().when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);
    }

    @Test
    void testHandleContentPublish_ConcurrentTaskCountControl() {
        // Given - 模拟Redis锁获取成功
        when(valueOperations.setIfAbsent(anyString(), anyString(), any(java.time.Duration.class)))
                .thenReturn(true);
        when(followPushDomainService.getTask(anyString())).thenReturn(null);

        // 模拟线程池拒绝执行，测试并发控制
        doThrow(new RejectedExecutionException("线程池已满"))
                .when(taskExecutor).execute(any(Runnable.class));

        // When & Then - 应该抛出异常并正确处理计数器
        assertThrows(RuntimeException.class, () -> {
            followPushApplicationService.handleContentPublish(
                    PUBLISHER_ID, CONTENT_ID, CONTENT_TYPE,
                    CONTENT_TITLE, CONTENT_SUMMARY, CONTENT_COVER, CONTENT_URL);
        });

        // 验证任务失败被标记（可能被调用多次）
        verify(followPushDomainService, atLeastOnce()).markTaskFailed(anyString(), contains("线程池异常"));
    }

    @Test
    void testHandleContentPublish_RedisLockFailure() {
        // Given - 模拟Redis锁获取失败
        when(valueOperations.setIfAbsent(anyString(), anyString(), any(java.time.Duration.class)))
                .thenReturn(false);

        // When & Then - 应该抛出异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            followPushApplicationService.handleContentPublish(
                    PUBLISHER_ID, CONTENT_ID, CONTENT_TYPE,
                    CONTENT_TITLE, CONTENT_SUMMARY, CONTENT_COVER, CONTENT_URL);
        });

        assertTrue(exception.getMessage().contains("获取处理锁失败"));
        
        // 验证没有创建任务
        verify(followPushDomainService, never()).createProcessTask(anyString(), anyLong(), anyLong(),
                any(ContentTypeEnum.class), anyString(), anyString(), anyString(), anyString());
    }

    @Test
    void testHandleContentPublish_RedisConnectionException() {
        // Given - 模拟Redis连接异常
        when(valueOperations.setIfAbsent(anyString(), anyString(), any(java.time.Duration.class)))
                .thenThrow(new RuntimeException("Redis连接异常"));

        // When & Then - 应该抛出异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            followPushApplicationService.handleContentPublish(
                    PUBLISHER_ID, CONTENT_ID, CONTENT_TYPE,
                    CONTENT_TITLE, CONTENT_SUMMARY, CONTENT_COVER, CONTENT_URL);
        });

        assertTrue(exception.getMessage().contains("获取处理锁失败"));
    }

    @Test
    void testHandleContentPublish_TaskCreationFailure() {
        // Given - 模拟Redis锁获取成功
        when(valueOperations.setIfAbsent(anyString(), anyString(), any(java.time.Duration.class)))
                .thenReturn(true);
        when(followPushDomainService.getTask(anyString())).thenReturn(null);
        
        // 模拟任务创建失败
        doThrow(new RuntimeException("数据库异常"))
                .when(followPushDomainService).createProcessTask(anyString(), anyLong(), anyLong(),
                        any(ContentTypeEnum.class), anyString(), anyString(), anyString(), anyString());

        // When & Then - 应该抛出异常
        assertThrows(RuntimeException.class, () -> {
            followPushApplicationService.handleContentPublish(
                    PUBLISHER_ID, CONTENT_ID, CONTENT_TYPE,
                    CONTENT_TITLE, CONTENT_SUMMARY, CONTENT_COVER, CONTENT_URL);
        });

        // 验证没有提交异步任务
        verify(taskExecutor, never()).execute(any(Runnable.class));
    }

    @Test
    void testRecoverContentPublishTask_ConcurrentControl() {
        // Given
        ContentPublishTask task = createTestTask("test_task_1", TaskStatusEnum.PROCESSING);

        // 模拟线程池拒绝执行
        doThrow(new RejectedExecutionException("线程池已满"))
                .when(taskExecutor).execute(any(Runnable.class));

        // When & Then - 应该抛出异常
        assertThrows(InvocationTargetException.class, () -> {
            // 使用反射调用私有方法
            java.lang.reflect.Method method = FollowPushApplicationServiceImpl.class
                    .getDeclaredMethod("recoverContentPublishTask", ContentPublishTask.class);
            method.setAccessible(true);
            method.invoke(followPushApplicationService, task);
        });

        // 验证任务失败被标记
        verify(followPushDomainService).markTaskFailed(eq("test_task_1"), contains("线程池异常"));
    }

    @Test
    void testSubmitAsyncTask_ThreadPoolRejection() {
        // Given - 模拟Redis锁获取成功
        when(valueOperations.setIfAbsent(anyString(), anyString(), any(java.time.Duration.class)))
                .thenReturn(true);
        when(followPushDomainService.getTask(anyString())).thenReturn(null);

        // 模拟线程池拒绝执行
        doThrow(new RejectedExecutionException("线程池已满"))
                .when(taskExecutor).execute(any(Runnable.class));

        // When & Then - 应该抛出异常并正确处理计数器
        assertThrows(RuntimeException.class, () -> {
            followPushApplicationService.handleContentPublish(
                    PUBLISHER_ID, CONTENT_ID, CONTENT_TYPE,
                    CONTENT_TITLE, CONTENT_SUMMARY, CONTENT_COVER, CONTENT_URL);
        });

        // 验证任务失败被标记（可能被调用多次）
        verify(followPushDomainService, atLeastOnce()).markTaskFailed(anyString(), contains("线程池异常"));

        // 验证任务创建成功
        verify(followPushDomainService).createProcessTask(anyString(), anyLong(), anyLong(),
                any(ContentTypeEnum.class), anyString(), anyString(), anyString(), anyString());
    }

    @Test
    void testHandleContentPublish_UnsafeUrlProtocol() {
        // Given - 使用不安全的URL协议
        String unsafeUrl = "file:///etc/passwd";

        // When & Then - 应该抛出参数验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            followPushApplicationService.handleContentPublish(
                    PUBLISHER_ID, CONTENT_ID, CONTENT_TYPE,
                    CONTENT_TITLE, CONTENT_SUMMARY, unsafeUrl, CONTENT_URL);
        });

        assertTrue(exception.getMessage().contains("URL协议不安全"));

        // 验证没有创建任务
        verify(followPushDomainService, never()).createProcessTask(anyString(), anyLong(), anyLong(),
                any(ContentTypeEnum.class), anyString(), anyString(), anyString(), anyString());
    }

    @Test
    void testHandleContentPublish_JavascriptUrl() {
        // Given - 使用javascript协议的URL
        String javascriptUrl = "javascript:alert('xss')";

        // When & Then - 应该抛出参数验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            followPushApplicationService.handleContentPublish(
                    PUBLISHER_ID, CONTENT_ID, CONTENT_TYPE,
                    CONTENT_TITLE, CONTENT_SUMMARY, CONTENT_COVER, javascriptUrl);
        });

        // 检查异常消息
        String message = exception.getMessage();
        System.out.println("Exception message: " + message);
        System.out.println("Exception type: " + exception.getClass().getSimpleName());

        assertTrue(message.contains("URL格式不正确") ||
                   message.contains("URL协议不安全") ||
                   message.contains("包含非法字符") ||
                   message.contains("线程池异常"),
                   "Expected URL validation error but got: " + message);

        // 验证没有创建任务
        verify(followPushDomainService, never()).createProcessTask(anyString(), anyLong(), anyLong(),
                any(ContentTypeEnum.class), anyString(), anyString(), anyString(), anyString());
    }

    @Test
    void testCleanupThreadLocal() {
        // When
        followPushApplicationService.cleanupThreadLocal();

        // Then - 应该正常执行，不抛出异常
        // 这个测试主要确保方法存在且可以正常调用
    }

    @Test
    void testGetSystemStatus() {
        // When
        java.util.Map<String, Object> status = followPushApplicationService.getSystemStatus();

        // Then
        assertNotNull(status);
        assertTrue(status.containsKey("currentTaskCount"));
        assertTrue(status.containsKey("maxConcurrentTasks"));
        assertTrue(status.containsKey("redisLockTimeoutMs"));
        assertTrue(status.containsKey("redisLockRetryIntervalMs"));
        assertTrue(status.containsKey("batchSize"));
    }

    private ContentPublishTask createTestTask(String taskId, TaskStatusEnum status) {
        ContentPublishTask task = new ContentPublishTask();
        task.setTaskId(taskId);
        task.setPublisherId(PUBLISHER_ID);
        task.setContentId(CONTENT_ID);
        task.setContentType(CONTENT_TYPE);
        task.setContentTitle(CONTENT_TITLE);
        task.setContentSummary(CONTENT_SUMMARY);
        task.setContentCover(CONTENT_COVER);
        task.setContentUrl(CONTENT_URL);
        task.setTaskStatus(status);
        return task;
    }
}
