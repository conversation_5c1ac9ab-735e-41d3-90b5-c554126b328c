package com.chenbang.social.service.application.impl;

import com.chenbang.social.service.domain.entity.TaskBatchError;
import com.chenbang.social.service.domain.repository.TaskBatchErrorRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 任务批次错误集成测试
 * 测试数据库实现的批次错误处理功能
 * <AUTHOR>
 * @since 2024-07-20
 */
@SpringBootTest
@ActiveProfiles("test")
class TaskBatchErrorIntegrationTest {

    @Autowired
    private TaskBatchErrorRepository taskBatchErrorRepository;

    @Test
    void testSaveAndFindTaskBatchError() {
        // Given
        String taskId = "test_task_001";
        TaskBatchError error = TaskBatchError.create(
                taskId, "IN_APP_MESSAGE", 0, "Test error message", "default", "system");

        // When
        Long errorId = taskBatchErrorRepository.save(error);

        // Then
        assertNotNull(errorId);
        assertTrue(errorId > 0);

        // 验证查询
        TaskBatchError savedError = taskBatchErrorRepository.findById(errorId);
        assertNotNull(savedError);
        assertEquals(taskId, savedError.getTaskId());
        assertEquals("IN_APP_MESSAGE", savedError.getPushType());
        assertEquals(0, savedError.getBatchOffset());
        assertEquals("Test error message", savedError.getErrorMessage());
        assertEquals(0, savedError.getRetryCount());
        assertFalse(savedError.getIsFixed());
    }

    @Test
    void testFindByTaskId() {
        // Given
        String taskId = "test_task_002";
        TaskBatchError error1 = TaskBatchError.create(
                taskId, "IN_APP_MESSAGE", 0, "Error 1", "default", "system");
        TaskBatchError error2 = TaskBatchError.create(
                taskId, "FEED_STREAM", 100, "Error 2", "default", "system");

        // When
        taskBatchErrorRepository.save(error1);
        taskBatchErrorRepository.save(error2);

        // Then
        List<TaskBatchError> errors = taskBatchErrorRepository.findByTaskId(taskId);
        assertEquals(2, errors.size());
    }

    @Test
    void testUpdateTaskBatchError() {
        // Given
        String taskId = "test_task_003";
        TaskBatchError error = TaskBatchError.create(
                taskId, "IN_APP_MESSAGE", 0, "Test error", "default", "system");
        Long errorId = taskBatchErrorRepository.save(error);

        // When
        error.incrementRetry("system");
        taskBatchErrorRepository.update(error);

        // Then
        TaskBatchError updatedError = taskBatchErrorRepository.findById(errorId);
        assertEquals(1, updatedError.getRetryCount());
    }

    @Test
    void testFindUnfixedErrors() {
        // Given
        String taskId1 = "test_task_004";
        String taskId2 = "test_task_005";
        
        TaskBatchError unfixedError = TaskBatchError.create(
                taskId1, "IN_APP_MESSAGE", 0, "Unfixed error", "default", "system");
        TaskBatchError fixedError = TaskBatchError.create(
                taskId2, "FEED_STREAM", 0, "Fixed error", "default", "system");
        
        taskBatchErrorRepository.save(unfixedError);
        Long fixedErrorId = taskBatchErrorRepository.save(fixedError);
        
        // 标记一个错误为已修复
        fixedError.markFixed("system");
        taskBatchErrorRepository.update(fixedError);

        // When
        List<TaskBatchError> unfixedErrors = taskBatchErrorRepository.findUnfixedErrors(10);

        // Then
        assertTrue(unfixedErrors.size() >= 1);
        assertTrue(unfixedErrors.stream().anyMatch(e -> e.getTaskId().equals(taskId1)));
        assertFalse(unfixedErrors.stream().anyMatch(e -> e.getTaskId().equals(taskId2)));
    }

    @Test
    void testDeleteExpiredErrors() {
        // Given
        String taskId = "test_task_006";
        TaskBatchError error = TaskBatchError.create(
                taskId, "IN_APP_MESSAGE", 0, "Old error", "default", "system");
        
        // 手动设置创建时间为过期时间
        error.setCreateTime(LocalDateTime.now().minusDays(10));
        taskBatchErrorRepository.save(error);

        // When
        LocalDateTime expireTime = LocalDateTime.now().minusDays(7);
        int deletedCount = taskBatchErrorRepository.deleteExpiredErrors(expireTime);

        // Then
        assertTrue(deletedCount >= 1);
    }
}
