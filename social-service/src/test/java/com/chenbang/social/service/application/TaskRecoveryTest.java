package com.chenbang.social.service.application;

import com.chenbang.social.api.enums.ContentTypeEnum;
import com.chenbang.social.api.enums.TaskStatusEnum;
import com.chenbang.social.service.application.impl.TaskRecoveryApplicationServiceImpl;
import com.chenbang.social.service.domain.entity.ContentPublishTask;
import com.chenbang.social.service.domain.service.FollowPushDomainService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 任务恢复测试
 * <AUTHOR>
 * @since 2024-07-18
 */
@ExtendWith(MockitoExtension.class)
class TaskRecoveryTest {
    
    @Mock
    private FollowPushDomainService followPushDomainService;
    
    @Mock
    private FollowPushApplicationService followPushApplicationService;
    
    @InjectMocks
    private TaskRecoveryApplicationServiceImpl taskRecoveryApplicationService;
    
    private ContentPublishTask createTestTask(String taskId, TaskStatusEnum status) {
        ContentPublishTask task = new ContentPublishTask();
        task.setId(1L);
        task.setTaskId(taskId);
        task.setPublisherId(123L);
        task.setContentId(456L);
        task.setContentType(ContentTypeEnum.MOMENT);
        task.setContentTitle("测试内容");
        task.setContentSummary("测试摘要");
        task.setContentCover("http://example.com/cover.jpg");
        task.setContentUrl("http://example.com/content");
        task.setTaskStatus(status);
        task.setInAppMessageProgress(0);
        task.setFeedStreamProgress(0);
        task.setInAppMessageOffset(0);
        task.setFeedStreamOffset(0);
        task.setCreateTime(LocalDateTime.now());
        task.setUpdateTime(LocalDateTime.now());
        return task;
    }
    
    @Test
    void testRecoverAllTasks() {
        // Given
        ContentPublishTask processingTask = createTestTask("task1", TaskStatusEnum.PROCESSING);
        ContentPublishTask inAppCompletedTask = createTestTask("task2", TaskStatusEnum.IN_APP_COMPLETED);
        
        List<ContentPublishTask> recoverableTasks = Arrays.asList(processingTask, inAppCompletedTask);
        when(followPushDomainService.findRecoverableTasks(100)).thenReturn(recoverableTasks);
        
        // When
        int recoveredCount = taskRecoveryApplicationService.recoverAllTasks();
        
        // Then
        assertEquals(2, recoveredCount);
        verify(followPushApplicationService, times(2)).handleContentPublish(
                anyLong(), anyLong(), any(ContentTypeEnum.class), 
                anyString(), anyString(), anyString(), anyString()
        );
    }
    
    @Test
    void testRecoverSpecificTask() {
        // Given
        String taskId = "test_task_123";
        ContentPublishTask task = createTestTask(taskId, TaskStatusEnum.PROCESSING);
        
        when(followPushDomainService.getTask(taskId)).thenReturn(task);
        
        // When
        boolean result = taskRecoveryApplicationService.recoverTask(taskId);
        
        // Then
        assertTrue(result);
        verify(followPushApplicationService).handleContentPublish(
                eq(123L), eq(456L), eq(ContentTypeEnum.MOMENT),
                eq("测试内容"), eq("测试摘要"), eq("http://example.com/cover.jpg"), eq("http://example.com/content")
        );
    }
    
    @Test
    void testRecoverNonExistentTask() {
        // Given
        String taskId = "non_existent_task";
        when(followPushDomainService.getTask(taskId)).thenReturn(null);
        
        // When
        boolean result = taskRecoveryApplicationService.recoverTask(taskId);
        
        // Then
        assertFalse(result);
        verify(followPushApplicationService, never()).handleContentPublish(
                anyLong(), anyLong(), any(ContentTypeEnum.class), 
                anyString(), anyString(), anyString(), anyString()
        );
    }
    
    @Test
    void testRecoverCompletedTask() {
        // Given
        String taskId = "completed_task";
        ContentPublishTask task = createTestTask(taskId, TaskStatusEnum.COMPLETED);
        
        when(followPushDomainService.getTask(taskId)).thenReturn(task);
        
        // When
        boolean result = taskRecoveryApplicationService.recoverTask(taskId);
        
        // Then
        assertFalse(result);
        verify(followPushApplicationService, never()).handleContentPublish(
                anyLong(), anyLong(), any(ContentTypeEnum.class), 
                anyString(), anyString(), anyString(), anyString()
        );
    }
    
    @Test
    void testCancelTask() {
        // Given
        String taskId = "test_task_123";
        ContentPublishTask task = createTestTask(taskId, TaskStatusEnum.PROCESSING);
        
        when(followPushDomainService.getTask(taskId)).thenReturn(task);
        
        // When
        boolean result = taskRecoveryApplicationService.cancelTask(taskId);
        
        // Then
        assertTrue(result);
        verify(followPushDomainService).markTaskFailed(taskId, "任务已取消");
    }
    
    @Test
    void testRetryFailedTask() {
        // Given
        String taskId = "failed_task";
        ContentPublishTask task = createTestTask(taskId, TaskStatusEnum.FAILED);
        
        when(followPushDomainService.getTask(taskId)).thenReturn(task);
        
        // When
        boolean result = taskRecoveryApplicationService.retryFailedTask(taskId);
        
        // Then
        assertTrue(result);
        verify(followPushApplicationService).handleContentPublish(
                eq(123L), eq(456L), eq(ContentTypeEnum.MOMENT),
                eq("测试内容"), eq("测试摘要"), eq("http://example.com/cover.jpg"), eq("http://example.com/content")
        );
    }
    
    @Test
    void testListRecoverableTasks() {
        // Given
        List<ContentPublishTask> recoverableTasks = Arrays.asList(
                createTestTask("task1", TaskStatusEnum.PROCESSING),
                createTestTask("task2", TaskStatusEnum.IN_APP_COMPLETED)
        );
        
        when(followPushDomainService.findRecoverableTasks(50)).thenReturn(recoverableTasks);
        
        // When
        List<ContentPublishTask> result = taskRecoveryApplicationService.listRecoverableTasks(50);
        
        // Then
        assertEquals(2, result.size());
        assertEquals("task1", result.get(0).getTaskId());
        assertEquals("task2", result.get(1).getTaskId());
    }
}
