package com.chenbang.social.service.infrastructure.security;

import com.chenbang.social.service.config.FollowPushProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * 安全服务
 * <AUTHOR>
 * @since 2024-07-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SecurityService {
    
    private final FollowPushProperties followPushProperties;
    
    // 敏感词库（实际项目中应该从数据库或配置文件加载）
    private static final Set<String> SENSITIVE_WORDS = new HashSet<>(Arrays.asList(
            "敏感词1", "敏感词2", "敏感词3"
    ));
    
    // SQL注入检测模式
    private static final Pattern SQL_INJECTION_PATTERN = Pattern.compile(
            "(?i)(union|select|insert|update|delete|drop|create|alter|exec|execute|script|javascript|vbscript)",
            Pattern.CASE_INSENSITIVE
    );
    
    // XSS攻击检测模式
    private static final Pattern XSS_PATTERN = Pattern.compile(
            "(?i)(<script|</script|javascript:|vbscript:|onload=|onerror=|onclick=)",
            Pattern.CASE_INSENSITIVE
    );
    
    /**
     * 检查IP是否在白名单中
     */
    public boolean isIpAllowed(String ip) {
        if (!followPushProperties.getSecurity().isEnableIpWhitelist()) {
            return true;
        }
        
        String[] whitelist = followPushProperties.getSecurity().getIpWhitelist();
        if (whitelist == null || whitelist.length == 0) {
            return true;
        }
        
        for (String allowedIp : whitelist) {
            if (allowedIp.equals(ip) || isIpInRange(ip, allowedIp)) {
                return true;
            }
        }
        
        log.warn("IP不在白名单中，ip: {}", ip);
        return false;
    }
    
    /**
     * 检查IP是否在指定范围内
     */
    private boolean isIpInRange(String ip, String range) {
        // 简单的CIDR检查实现
        if (range.contains("/")) {
            // TODO: 实现CIDR范围检查
            return false;
        }
        return ip.equals(range);
    }
    
    /**
     * 敏感词过滤
     */
    public String filterSensitiveWords(String content) {
        if (!followPushProperties.getSecurity().isEnableSensitiveWordFilter() || !StringUtils.hasText(content)) {
            return content;
        }
        
        String filteredContent = content;
        for (String sensitiveWord : SENSITIVE_WORDS) {
            if (filteredContent.contains(sensitiveWord)) {
                String replacement = "*".repeat(sensitiveWord.length());
                filteredContent = filteredContent.replace(sensitiveWord, replacement);
                log.info("检测到敏感词并已过滤，原词: {}", sensitiveWord);
            }
        }
        
        return filteredContent;
    }
    
    /**
     * 检查是否包含敏感词
     */
    public boolean containsSensitiveWords(String content) {
        if (!followPushProperties.getSecurity().isEnableSensitiveWordFilter() || !StringUtils.hasText(content)) {
            return false;
        }
        
        for (String sensitiveWord : SENSITIVE_WORDS) {
            if (content.contains(sensitiveWord)) {
                log.warn("检测到敏感词，content: {}, sensitiveWord: {}", content, sensitiveWord);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * SQL注入检测
     */
    public boolean containsSqlInjection(String input) {
        if (!StringUtils.hasText(input)) {
            return false;
        }
        
        boolean detected = SQL_INJECTION_PATTERN.matcher(input).find();
        if (detected) {
            log.warn("检测到SQL注入攻击，input: {}", input);
        }
        
        return detected;
    }
    
    /**
     * XSS攻击检测
     */
    public boolean containsXss(String input) {
        if (!StringUtils.hasText(input)) {
            return false;
        }
        
        boolean detected = XSS_PATTERN.matcher(input).find();
        if (detected) {
            log.warn("检测到XSS攻击，input: {}", input);
        }
        
        return detected;
    }
    
    /**
     * 输入验证
     */
    public ValidationResult validateInput(String input, String fieldName) {
        if (!StringUtils.hasText(input)) {
            return ValidationResult.success();
        }
        
        // 长度检查
        if (input.length() > 10000) {
            return ValidationResult.failure(fieldName + "长度超过限制");
        }
        
        // SQL注入检查
        if (containsSqlInjection(input)) {
            return ValidationResult.failure(fieldName + "包含非法字符");
        }
        
        // XSS攻击检查
        if (containsXss(input)) {
            return ValidationResult.failure(fieldName + "包含非法脚本");
        }
        
        // 敏感词检查
        if (containsSensitiveWords(input)) {
            return ValidationResult.failure(fieldName + "包含敏感词");
        }
        
        return ValidationResult.success();
    }
    
    /**
     * 内容安全检查
     */
    public ContentSecurityResult checkContentSecurity(String title, String content, String summary) {
        ContentSecurityResult result = new ContentSecurityResult();
        
        // 检查标题
        ValidationResult titleResult = validateInput(title, "标题");
        if (!titleResult.isValid()) {
            result.setValid(false);
            result.setReason(titleResult.getErrorMessage());
            return result;
        }
        
        // 检查内容
        ValidationResult contentResult = validateInput(content, "内容");
        if (!contentResult.isValid()) {
            result.setValid(false);
            result.setReason(contentResult.getErrorMessage());
            return result;
        }
        
        // 检查摘要
        ValidationResult summaryResult = validateInput(summary, "摘要");
        if (!summaryResult.isValid()) {
            result.setValid(false);
            result.setReason(summaryResult.getErrorMessage());
            return result;
        }
        
        // 过滤敏感词
        result.setFilteredTitle(filterSensitiveWords(title));
        result.setFilteredContent(filterSensitiveWords(content));
        result.setFilteredSummary(filterSensitiveWords(summary));
        result.setValid(true);
        
        return result;
    }
    
    /**
     * 验证结果
     */
    public static class ValidationResult {
        private boolean valid;
        private String errorMessage;
        
        public static ValidationResult success() {
            ValidationResult result = new ValidationResult();
            result.valid = true;
            return result;
        }
        
        public static ValidationResult failure(String errorMessage) {
            ValidationResult result = new ValidationResult();
            result.valid = false;
            result.errorMessage = errorMessage;
            return result;
        }
        
        public boolean isValid() { return valid; }
        public String getErrorMessage() { return errorMessage; }
    }
    
    /**
     * 内容安全检查结果
     */
    public static class ContentSecurityResult {
        private boolean valid;
        private String reason;
        private String filteredTitle;
        private String filteredContent;
        private String filteredSummary;
        
        // Getters and Setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
        public String getFilteredTitle() { return filteredTitle; }
        public void setFilteredTitle(String filteredTitle) { this.filteredTitle = filteredTitle; }
        public String getFilteredContent() { return filteredContent; }
        public void setFilteredContent(String filteredContent) { this.filteredContent = filteredContent; }
        public String getFilteredSummary() { return filteredSummary; }
        public void setFilteredSummary(String filteredSummary) { this.filteredSummary = filteredSummary; }
    }
}
