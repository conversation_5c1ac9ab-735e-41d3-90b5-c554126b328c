package com.chenbang.social.service.infrastructure.ratelimit;

import com.chenbang.social.service.config.FollowPushProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * 限流服务
 * <AUTHOR>
 * @since 2024-07-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RateLimitService {
    
    private final RedisTemplate<String, Object> redisTemplate;
    private final FollowPushProperties followPushProperties;
    
    // Lua脚本实现滑动窗口限流
    private static final String RATE_LIMIT_SCRIPT = 
            "local key = KEYS[1]\n" +
            "local window = tonumber(ARGV[1])\n" +
            "local limit = tonumber(ARGV[2])\n" +
            "local current = tonumber(ARGV[3])\n" +
            "\n" +
            "redis.call('zremrangebyscore', key, 0, current - window)\n" +
            "local count = redis.call('zcard', key)\n" +
            "\n" +
            "if count < limit then\n" +
            "    redis.call('zadd', key, current, current)\n" +
            "    redis.call('expire', key, math.ceil(window / 1000))\n" +
            "    return {1, limit - count - 1}\n" +
            "else\n" +
            "    return {0, 0}\n" +
            "end";
    
    private final DefaultRedisScript<Object> rateLimitScript = new DefaultRedisScript<>(RATE_LIMIT_SCRIPT, Object.class);
    
    /**
     * 检查是否超过限流
     * @param key 限流key
     * @param windowMs 时间窗口（毫秒）
     * @param limit 限制次数
     * @return 是否允许通过
     */
    public boolean isAllowed(String key, long windowMs, int limit) {
        if (!followPushProperties.getSecurity().isEnableRateLimit()) {
            return true;
        }
        
        try {
            long current = System.currentTimeMillis();
            String rateLimitKey = "rate_limit:" + key;
            
            Object result = redisTemplate.execute(rateLimitScript, 
                    Collections.singletonList(rateLimitKey), 
                    windowMs, limit, current);
            
            if (result instanceof java.util.List) {
                @SuppressWarnings("unchecked")
                java.util.List<Long> list = (java.util.List<Long>) result;
                boolean allowed = list.get(0) == 1;
                long remaining = list.get(1);
                
                if (!allowed) {
                    log.warn("请求被限流，key: {}, limit: {}, window: {}ms", key, limit, windowMs);
                }
                
                return allowed;
            }
            
        } catch (Exception e) {
            log.error("限流检查失败，key: {}", key, e);
            // 限流服务异常时，允许请求通过，避免影响业务
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查用户请求限流
     */
    public boolean checkUserRateLimit(Long userId) {
        String key = "user:" + userId;
        return isAllowed(key, 60 * 1000, followPushProperties.getSecurity().getMaxRequestsPerMinute());
    }
    
    /**
     * 检查IP请求限流
     */
    public boolean checkIpRateLimit(String ip) {
        String key = "ip:" + ip;
        return isAllowed(key, 60 * 1000, followPushProperties.getSecurity().getMaxRequestsPerMinute() * 10);
    }
    
    /**
     * 检查内容发布限流
     */
    public boolean checkContentPublishRateLimit(Long publisherId) {
        String key = "content_publish:" + publisherId;
        // 每个发布者每分钟最多发布10次内容
        return isAllowed(key, 60 * 1000, 10);
    }
    
    /**
     * 检查任务处理限流
     */
    public boolean checkTaskProcessRateLimit() {
        String key = "task_process";
        // 系统每秒最多处理100个任务
        return isAllowed(key, 1000, 100);
    }
}
