package com.chenbang.social.service.infrastructure.persistence.mapper;

import com.chenbang.social.service.infrastructure.persistence.po.UserFeedPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户Feed流Mapper
 * <AUTHOR>
 * @since 2024-07-14
 */
@Mapper
public interface UserFeedMapper {
    
    /**
     * 插入Feed记录
     * @param feed Feed信息
     * @return 影响行数
     */
    int insert(UserFeedPO feed);
    
    /**
     * 批量插入Feed记录
     * @param feeds Feed列表
     * @return 影响行数
     */
    int batchInsert(@Param("feeds") List<UserFeedPO> feeds);
    
    /**
     * 更新Feed记录
     * @param feed Feed信息
     * @return 影响行数
     */
    int update(UserFeedPO feed);
    
    /**
     * 根据ID查询Feed记录
     * @param id Feed ID
     * @return Feed信息
     */
    UserFeedPO selectById(@Param("id") Long id);
    
    /**
     * 根据用户ID查询Feed流（时间线分页）
     * @param userId 用户ID
     * @param lastTimestamp 最后一条记录的时间戳
     * @param limit 限制数量
     * @param contentType 内容类型过滤
     * @return Feed列表
     */
    List<UserFeedPO> selectByUserIdWithTimeline(@Param("userId") Long userId,
                                              @Param("lastTimestamp") Long lastTimestamp,
                                              @Param("limit") Integer limit,
                                              @Param("contentType") Integer contentType);

    /**
     * 根据用户ID分页查询Feed流
     * @param userId 用户ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @param contentType 内容类型过滤
     * @return Feed列表
     */
    List<UserFeedPO> selectByUserId(@Param("userId") Long userId,
                                  @Param("offset") Integer offset,
                                  @Param("limit") Integer limit,
                                  @Param("contentType") Integer contentType);
    
    /**
     * 删除过期的Feed记录
     * @param expireTime 过期时间
     * @return 删除数量
     */
    Integer deleteExpiredFeeds(@Param("expireTime") LocalDateTime expireTime);
    
    /**
     * 根据ID删除Feed记录
     * @param id Feed ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
}
