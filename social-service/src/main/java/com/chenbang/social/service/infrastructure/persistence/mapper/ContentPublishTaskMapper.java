package com.chenbang.social.service.infrastructure.persistence.mapper;

import com.chenbang.social.service.infrastructure.persistence.po.ContentPublishTaskPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 内容发布任务Mapper
 * <AUTHOR>
 * @since 2024-07-15
 */
@Mapper
public interface ContentPublishTaskMapper {
    
    /**
     * 插入任务
     * @param task 任务PO
     * @return 影响行数
     */
    int insert(ContentPublishTaskPO task);
    
    /**
     * 更新任务
     * @param task 任务PO
     * @return 影响行数
     */
    int update(ContentPublishTaskPO task);
    
    /**
     * 根据任务ID查询任务
     * @param taskId 任务ID
     * @return 任务PO
     */
    ContentPublishTaskPO selectByTaskId(@Param("taskId") String taskId);
    
    /**
     * 根据ID查询任务
     * @param id 主键ID
     * @return 任务PO
     */
    ContentPublishTaskPO selectById(@Param("id") Long id);
    
    /**
     * 查询任务列表
     * @param publisherId 发布者ID
     * @param contentId 内容ID
     * @param status 任务状态
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 任务列表
     */
    List<ContentPublishTaskPO> selectTasks(
            @Param("publisherId") Long publisherId,
            @Param("contentId") Long contentId,
            @Param("status") String status,
            @Param("offset") int offset,
            @Param("limit") int limit);
    
    /**
     * 查询处理中的任务数量
     * @return 处理中的任务数量
     */
    int countProcessingTasks();
    
    /**
     * 查询任务统计信息
     * @return 任务统计信息
     */
    Map<String, Object> getTaskStatistics();
    
    /**
     * 查询发布者的任务统计信息
     * @param publisherId 发布者ID
     * @return 任务统计信息
     */
    Map<String, Object> getPublisherTaskStatistics(@Param("publisherId") Long publisherId);
    
    /**
     * 删除过期任务
     * @param expireTime 过期时间
     * @return 删除数量
     */
    int deleteExpiredTasks(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 查询可恢复的任务列表
     * @param statuses 可恢复的状态列表
     * @param limit 限制数量
     * @return 可恢复的任务列表
     */
    List<ContentPublishTaskPO> selectRecoverableTasks(
            @Param("statuses") List<String> statuses,
            @Param("limit") int limit);
}
