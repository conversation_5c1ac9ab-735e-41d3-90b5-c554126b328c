package com.chenbang.social.service.infrastructure.repository;

import com.chenbang.social.api.enums.TaskStatusEnum;
import com.chenbang.social.service.domain.entity.ContentPublishTask;
import com.chenbang.social.service.domain.repository.ContentPublishTaskRepository;
import com.chenbang.social.service.infrastructure.persistence.mapper.ContentPublishTaskMapper;
import com.chenbang.social.service.infrastructure.persistence.po.ContentPublishTaskPO;
import com.chenbang.social.service.infrastructure.repository.assembler.ContentPublishTaskAssembler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 内容发布任务仓储实现（数据库版本）
 * <AUTHOR>
 * @since 2024-07-15
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ContentPublishTaskRepositoryImpl implements ContentPublishTaskRepository {

    private final ContentPublishTaskMapper contentPublishTaskMapper;
    private final ContentPublishTaskAssembler contentPublishTaskAssembler;
    
    @Override
    public Long save(ContentPublishTask task) {
        ContentPublishTaskPO po = contentPublishTaskAssembler.convertToPO(task);
        contentPublishTaskMapper.insert(po);

        Long id = po.getId();
        task.setId(id);

        log.debug("保存任务成功，taskId: {}, id: {}", task.getTaskId(), id);
        return id;
    }
    
    @Override
    public void update(ContentPublishTask task) {
        ContentPublishTaskPO po = contentPublishTaskAssembler.convertToPO(task);
        contentPublishTaskMapper.update(po);

        log.debug("更新任务成功，taskId: {}, id: {}", task.getTaskId(), task.getId());
    }

    @Override
    public ContentPublishTask findByTaskId(String taskId) {
        ContentPublishTaskPO po = contentPublishTaskMapper.selectByTaskId(taskId);
        return contentPublishTaskAssembler.convertToEntity(po);
    }

    @Override
    public ContentPublishTask findById(Long id) {
        ContentPublishTaskPO po = contentPublishTaskMapper.selectById(id);
        return contentPublishTaskAssembler.convertToEntity(po);
    }
    
    @Override
    public List<ContentPublishTask> findTasks(Long publisherId, Long contentId, String status, int offset, int limit) {
        List<ContentPublishTaskPO> pos = contentPublishTaskMapper.selectTasks(publisherId, contentId, status, offset, limit);
        return pos.stream()
                .map(contentPublishTaskAssembler::convertToEntity)
                .collect(Collectors.toList());
    }

    @Override
    public int countProcessingTasks() {
        return contentPublishTaskMapper.countProcessingTasks();
    }
    
    @Override
    public TaskStatistics getTaskStatistics() {
        Map<String, Object> statsMap = contentPublishTaskMapper.getTaskStatistics();
        return convertMapToTaskStatistics(statsMap);
    }
    
    @Override
    public TaskStatistics getPublisherTaskStatistics(Long publisherId) {
        Map<String, Object> statsMap = contentPublishTaskMapper.getPublisherTaskStatistics(publisherId);
        return convertMapToTaskStatistics(statsMap);
    }

    /**
     * 将Map转换为TaskStatistics
     */
    private TaskStatistics convertMapToTaskStatistics(Map<String, Object> statsMap) {
        TaskStatistics stats = new TaskStatistics();

        stats.setTotalTasks(getLongValue(statsMap, "total_tasks"));
        stats.setPendingTasks(getLongValue(statsMap, "pending_tasks"));
        stats.setProcessingTasks(getLongValue(statsMap, "processing_tasks"));
        stats.setCompletedTasks(getLongValue(statsMap, "completed_tasks"));
        stats.setFailedTasks(getLongValue(statsMap, "failed_tasks"));
        stats.setCancelledTasks(getLongValue(statsMap, "cancelled_tasks"));
        stats.setAvgProcessingTime(getDoubleValue(statsMap, "avg_processing_time"));
        stats.setMaxProcessingTime(getLongValue(statsMap, "max_processing_time"));
        stats.setMinProcessingTime(getLongValue(statsMap, "min_processing_time"));
        stats.setTodayProcessedTasks(getLongValue(statsMap, "today_processed_tasks"));
        stats.setTodayFailedTasks(getLongValue(statsMap, "today_failed_tasks"));
        stats.setStatisticsTime(LocalDateTime.now());

        return stats;
    }

    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return 0L;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        return 0L;
    }

    private Double getDoubleValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return 0.0;
        }
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return 0.0;
    }
    
    @Override
    public int deleteExpiredTasks(LocalDateTime expireTime) {
        int deletedCount = contentPublishTaskMapper.deleteExpiredTasks(expireTime);
        log.info("清理过期任务完成，删除数量: {}", deletedCount);
        return deletedCount;
    }

    @Override
    public List<ContentPublishTask> findRecoverableTasks(int limit) {
        // 查询状态为PROCESSING、IN_APP_COMPLETED、FEED_COMPLETED的任务
        List<String> recoverableStatuses = Arrays.asList(
                TaskStatusEnum.PROCESSING.name(),
                TaskStatusEnum.IN_APP_COMPLETED.name(),
                TaskStatusEnum.FEED_COMPLETED.name()
        );

        List<ContentPublishTaskPO> pos = contentPublishTaskMapper.selectRecoverableTasks(recoverableStatuses, limit);
        return pos.stream()
                .map(contentPublishTaskAssembler::convertToEntity)
                .collect(Collectors.toList());
    }
}
