package com.chenbang.social.service.infrastructure.repository;

import com.chenbang.social.api.enums.TaskStatusEnum;
import com.chenbang.social.service.domain.entity.ContentPublishTask;
import com.chenbang.social.service.domain.repository.ContentPublishTaskRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 内容发布任务仓储内存实现（仅用于演示）
 * 实际项目中应该使用数据库实现
 * <AUTHOR>
 * @since 2024-07-18
 */
@Slf4j
@Repository
@ConditionalOnProperty(name = "social.repository.type", havingValue = "memory", matchIfMissing = true)
public class ContentPublishTaskRepositoryMemoryImpl implements ContentPublishTaskRepository {
    
    private final Map<Long, ContentPublishTask> taskById = new ConcurrentHashMap<>();
    private final Map<String, ContentPublishTask> taskByTaskId = new ConcurrentHashMap<>();
    private final AtomicLong idGenerator = new AtomicLong(1);
    
    @Override
    public Long save(ContentPublishTask task) {
        Long id = idGenerator.getAndIncrement();
        task.setId(id);
        task.setCreateTime(LocalDateTime.now());
        task.setUpdateTime(LocalDateTime.now());
        
        taskById.put(id, task);
        taskByTaskId.put(task.getTaskId(), task);
        
        log.debug("保存任务成功，taskId: {}, id: {}", task.getTaskId(), id);
        return id;
    }
    
    @Override
    public void update(ContentPublishTask task) {
        task.setUpdateTime(LocalDateTime.now());
        taskById.put(task.getId(), task);
        taskByTaskId.put(task.getTaskId(), task);
        
        log.debug("更新任务成功，taskId: {}, id: {}", task.getTaskId(), task.getId());
    }
    
    @Override
    public ContentPublishTask findByTaskId(String taskId) {
        return taskByTaskId.get(taskId);
    }
    
    @Override
    public ContentPublishTask findById(Long id) {
        return taskById.get(id);
    }
    
    @Override
    public List<ContentPublishTask> findTasks(Long publisherId, Long contentId, String status, int offset, int limit) {
        return taskById.values().stream()
                .filter(task -> publisherId == null || publisherId.equals(task.getPublisherId()))
                .filter(task -> contentId == null || contentId.equals(task.getContentId()))
                .filter(task -> status == null || status.equals(task.getTaskStatus().name()))
                .sorted((t1, t2) -> t2.getCreateTime().compareTo(t1.getCreateTime()))
                .skip(offset)
                .limit(limit)
                .collect(Collectors.toList());
    }
    
    @Override
    public int countProcessingTasks() {
        return (int) taskById.values().stream()
                .filter(task -> task.getTaskStatus() == TaskStatusEnum.PROCESSING)
                .count();
    }
    
    @Override
    public TaskStatistics getTaskStatistics() {
        TaskStatistics stats = new TaskStatistics();
        
        Map<TaskStatusEnum, Long> statusCounts = taskById.values().stream()
                .collect(Collectors.groupingBy(ContentPublishTask::getTaskStatus, Collectors.counting()));
        
        stats.setTotalTasks((long) taskById.size());
        stats.setPendingTasks(statusCounts.getOrDefault(TaskStatusEnum.PENDING, 0L));
        stats.setProcessingTasks(statusCounts.getOrDefault(TaskStatusEnum.PROCESSING, 0L));
        stats.setCompletedTasks(statusCounts.getOrDefault(TaskStatusEnum.COMPLETED, 0L));
        stats.setFailedTasks(statusCounts.getOrDefault(TaskStatusEnum.FAILED, 0L));
        stats.setCancelledTasks(statusCounts.getOrDefault(TaskStatusEnum.CANCELLED, 0L));
        stats.setStatisticsTime(LocalDateTime.now());
        
        return stats;
    }
    
    @Override
    public TaskStatistics getPublisherTaskStatistics(Long publisherId) {
        TaskStatistics stats = new TaskStatistics();
        
        List<ContentPublishTask> publisherTasks = taskById.values().stream()
                .filter(task -> publisherId.equals(task.getPublisherId()))
                .collect(Collectors.toList());
        
        Map<TaskStatusEnum, Long> statusCounts = publisherTasks.stream()
                .collect(Collectors.groupingBy(ContentPublishTask::getTaskStatus, Collectors.counting()));
        
        stats.setTotalTasks((long) publisherTasks.size());
        stats.setPendingTasks(statusCounts.getOrDefault(TaskStatusEnum.PENDING, 0L));
        stats.setProcessingTasks(statusCounts.getOrDefault(TaskStatusEnum.PROCESSING, 0L));
        stats.setCompletedTasks(statusCounts.getOrDefault(TaskStatusEnum.COMPLETED, 0L));
        stats.setFailedTasks(statusCounts.getOrDefault(TaskStatusEnum.FAILED, 0L));
        stats.setCancelledTasks(statusCounts.getOrDefault(TaskStatusEnum.CANCELLED, 0L));
        stats.setStatisticsTime(LocalDateTime.now());
        
        return stats;
    }
    
    @Override
    public int deleteExpiredTasks(LocalDateTime expireTime) {
        List<ContentPublishTask> expiredTasks = taskById.values().stream()
                .filter(task -> task.getCreateTime().isBefore(expireTime))
                .collect(Collectors.toList());
        
        for (ContentPublishTask task : expiredTasks) {
            taskById.remove(task.getId());
            taskByTaskId.remove(task.getTaskId());
        }
        
        log.info("清理过期任务完成，删除数量: {}", expiredTasks.size());
        return expiredTasks.size();
    }
    
    @Override
    public List<ContentPublishTask> findRecoverableTasks(int limit) {
        return taskById.values().stream()
                .filter(task -> task.canRecover())
                .sorted((t1, t2) -> t1.getCreateTime().compareTo(t2.getCreateTime()))
                .limit(limit)
                .collect(Collectors.toList());
    }
}
