package com.chenbang.social.service.application;

import com.chenbang.social.service.domain.entity.ContentPublishTask;
import com.chenbang.social.service.domain.entity.TaskBatchError;

import java.util.List;

/**
 * 任务管理应用服务接口
 * <AUTHOR>
 * @since 2024-07-15
 */
public interface TaskManagementApplicationService {
    
    /**
     * 获取任务详情
     * @param taskId 任务ID
     * @return 任务详情
     */
    ContentPublishTask getTaskDetail(String taskId);
    
    /**
     * 获取任务列表
     * @param publisherId 发布者ID
     * @param contentId 内容ID
     * @param status 任务状态
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 任务列表
     */
    List<ContentPublishTask> listTasks(Long publisherId, Long contentId, String status, int pageNum, int pageSize);
    
    /**
     * 取消任务
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean cancelTask(String taskId);
    
    /**
     * 重试任务
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean retryTask(String taskId);
    
    /**
     * 获取任务批次错误列表
     * @param taskId 任务ID
     * @return 批次错误列表
     */
    List<TaskBatchError> listTaskBatchErrors(String taskId);
    
    /**
     * 重试批次错误
     * @param errorId 错误ID
     * @return 是否成功
     */
    boolean retryBatchError(Long errorId);
    
    /**
     * 清理过期任务
     * @param days 保留天数
     * @return 清理数量
     */
    int cleanExpiredTasks(int days);
}
