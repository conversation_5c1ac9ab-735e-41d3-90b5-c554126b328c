package com.chenbang.social.service.infrastructure.repository.assembler;

import com.chenbang.social.api.enums.ContentTypeEnum;
import com.chenbang.social.api.enums.TaskStatusEnum;
import com.chenbang.social.service.domain.entity.ContentPublishTask;
import com.chenbang.social.service.infrastructure.persistence.po.ContentPublishTaskPO;
import org.springframework.stereotype.Component;

/**
 * 内容发布任务转换器
 * <AUTHOR>
 * @since 2024-07-15
 */
@Component
public class ContentPublishTaskAssembler {
    
    /**
     * 实体转PO
     */
    public ContentPublishTaskPO convertToPO(ContentPublishTask entity) {
        if (entity == null) {
            return null;
        }
        
        ContentPublishTaskPO po = new ContentPublishTaskPO();
        po.setId(entity.getId());
        po.setTaskId(entity.getTaskId());
        po.setPublisherId(entity.getPublisherId());
        po.setContentId(entity.getContentId());
        po.setContentType(entity.getContentType() != null ? entity.getContentType().getCode() : null);
        po.setContentTitle(entity.getContentTitle());
        po.setContentSummary(entity.getContentSummary());
        po.setContentCover(entity.getContentCover());
        po.setContentUrl(entity.getContentUrl());
        po.setTaskStatus(entity.getTaskStatus() != null ? entity.getTaskStatus().name() : null);
        po.setInAppMessageProgress(entity.getInAppMessageProgress());
        po.setFeedStreamProgress(entity.getFeedStreamProgress());
        po.setInAppMessageOffset(entity.getInAppMessageOffset());
        po.setFeedStreamOffset(entity.getFeedStreamOffset());
        po.setInAppMessageTotal(entity.getInAppMessageTotal());
        po.setFeedStreamTotal(entity.getFeedStreamTotal());
        po.setErrorMessage(entity.getErrorMessage());
        po.setStartTime(entity.getStartTime());
        po.setCompleteTime(entity.getCompleteTime());
        po.setTenantCode(entity.getTenantCode());
        po.setCreateTime(entity.getCreateTime());
        po.setUpdateTime(entity.getUpdateTime());
        po.setCreateBy(entity.getCreateBy());
        po.setUpdateBy(entity.getUpdateBy());
        
        return po;
    }
    
    /**
     * PO转实体
     */
    public ContentPublishTask convertToEntity(ContentPublishTaskPO po) {
        if (po == null) {
            return null;
        }
        
        ContentPublishTask entity = new ContentPublishTask();
        entity.setId(po.getId());
        entity.setTaskId(po.getTaskId());
        entity.setPublisherId(po.getPublisherId());
        entity.setContentId(po.getContentId());
        entity.setContentType(po.getContentType() != null ? ContentTypeEnum.fromCode(po.getContentType()) : null);
        entity.setContentTitle(po.getContentTitle());
        entity.setContentSummary(po.getContentSummary());
        entity.setContentCover(po.getContentCover());
        entity.setContentUrl(po.getContentUrl());
        entity.setTaskStatus(po.getTaskStatus() != null ? TaskStatusEnum.fromString(po.getTaskStatus()) : null);
        entity.setInAppMessageProgress(po.getInAppMessageProgress());
        entity.setFeedStreamProgress(po.getFeedStreamProgress());
        entity.setInAppMessageOffset(po.getInAppMessageOffset());
        entity.setFeedStreamOffset(po.getFeedStreamOffset());
        entity.setInAppMessageTotal(po.getInAppMessageTotal());
        entity.setFeedStreamTotal(po.getFeedStreamTotal());
        entity.setErrorMessage(po.getErrorMessage());
        entity.setStartTime(po.getStartTime());
        entity.setCompleteTime(po.getCompleteTime());
        entity.setTenantCode(po.getTenantCode());
        entity.setCreateTime(po.getCreateTime());
        entity.setUpdateTime(po.getUpdateTime());
        entity.setCreateBy(po.getCreateBy());
        entity.setUpdateBy(po.getUpdateBy());
        
        return entity;
    }
}
