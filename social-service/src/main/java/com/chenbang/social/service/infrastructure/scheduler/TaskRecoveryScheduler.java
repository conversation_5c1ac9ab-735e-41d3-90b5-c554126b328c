package com.chenbang.social.service.infrastructure.scheduler;

import com.chenbang.social.service.application.TaskRecoveryApplicationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 任务恢复定时调度器
 * <AUTHOR>
 * @since 2024-07-18
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "social.task.recovery.enabled", havingValue = "true", matchIfMissing = true)
public class TaskRecoveryScheduler {
    
    private final TaskRecoveryApplicationService taskRecoveryApplicationService;
    
    /**
     * 定时恢复处理中的任务
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void recoverTasks() {
        try {
            log.info("开始定时任务恢复");
            int recoveredCount = taskRecoveryApplicationService.recoverAllTasks();
            if (recoveredCount > 0) {
                log.info("定时任务恢复完成，恢复了 {} 个任务", recoveredCount);
            }
        } catch (Exception e) {
            log.error("定时任务恢复失败", e);
        }
    }
}
