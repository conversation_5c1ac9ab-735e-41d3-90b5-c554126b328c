package com.chenbang.social.service.infrastructure.repository.assembler;

import com.chenbang.social.api.enums.PushStatusEnum;
import com.chenbang.social.api.enums.PushTypeEnum;
import com.chenbang.social.service.domain.entity.PushMessageRecord;
import com.chenbang.social.service.infrastructure.persistence.po.PushMessageRecordPO;
import org.springframework.stereotype.Component;

/**
 * 推送消息记录数据转换器
 * <AUTHOR>
 * @since 2024-07-14
 */
@Component
public class PushMessageRecordAssembler {
    
    /**
     * 领域对象转持久化对象
     * @param record 领域对象
     * @return 持久化对象
     */
    public PushMessageRecordPO convertToPO(PushMessageRecord record) {
        if (record == null) {
            return null;
        }
        
        PushMessageRecordPO po = new PushMessageRecordPO();
        po.setId(record.getId());
        po.setUserId(record.getUserId());
        po.setPublisherId(record.getPublisherId());
        po.setContentId(record.getContentId());
        po.setPushType(record.getPushType() != null ? record.getPushType().getCode() : null);
        po.setPushStatus(record.getPushStatus() != null ? record.getPushStatus().getCode() : null);
        po.setMessageTitle(record.getMessageTitle());
        po.setMessageContent(record.getMessageContent());
        po.setPushTime(record.getPushTime());
        po.setFailReason(record.getFailReason());
        po.setRetryCount(record.getRetryCount());
        po.setTenantCode(record.getTenantCode());
        po.setCreateTime(record.getCreateTime());
        po.setUpdateTime(record.getUpdateTime());
        po.setCreateBy(record.getCreateBy());
        po.setUpdateBy(record.getUpdateBy());
        po.setDeleted(record.getDeleted());
        
        return po;
    }
    
    /**
     * 持久化对象转领域对象
     * @param po 持久化对象
     * @return 领域对象
     */
    public PushMessageRecord convertToEntity(PushMessageRecordPO po) {
        if (po == null) {
            return null;
        }
        
        PushMessageRecord record = new PushMessageRecord();
        record.setId(po.getId());
        record.setUserId(po.getUserId());
        record.setPublisherId(po.getPublisherId());
        record.setContentId(po.getContentId());
        record.setPushType(PushTypeEnum.getByCode(po.getPushType()));
        record.setPushStatus(PushStatusEnum.getByCode(po.getPushStatus()));
        record.setMessageTitle(po.getMessageTitle());
        record.setMessageContent(po.getMessageContent());
        record.setPushTime(po.getPushTime());
        record.setFailReason(po.getFailReason());
        record.setRetryCount(po.getRetryCount());
        record.setTenantCode(po.getTenantCode());
        record.setCreateTime(po.getCreateTime());
        record.setUpdateTime(po.getUpdateTime());
        record.setCreateBy(po.getCreateBy());
        record.setUpdateBy(po.getUpdateBy());
        record.setDeleted(po.getDeleted());
        
        return record;
    }
}
