package com.chenbang.social.service.infrastructure.persistence.mapper;

import com.chenbang.social.service.infrastructure.persistence.po.PushMessageRecordPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 推送消息记录Mapper
 * <AUTHOR>
 * @since 2024-07-14
 */
@Mapper
public interface PushMessageRecordMapper {
    
    /**
     * 插入记录
     * @param record 记录信息
     * @return 影响行数
     */
    int insert(PushMessageRecordPO record);
    
    /**
     * 批量插入记录
     * @param records 记录列表
     * @return 影响行数
     */
    int batchInsert(@Param("records") List<PushMessageRecordPO> records);
    
    /**
     * 更新记录
     * @param record 记录信息
     * @return 影响行数
     */
    int update(PushMessageRecordPO record);
    
    /**
     * 根据ID查询记录
     * @param id 记录ID
     * @return 记录信息
     */
    PushMessageRecordPO selectById(@Param("id") Long id);
    
    /**
     * 根据用户ID分页查询记录
     * @param userId 用户ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 记录列表
     */
    List<PushMessageRecordPO> selectByUserId(@Param("userId") Long userId, 
                                           @Param("offset") Integer offset, 
                                           @Param("limit") Integer limit);
    
    /**
     * 查询失败的推送记录（用于重试）
     * @param limit 限制数量
     * @return 记录列表
     */
    List<PushMessageRecordPO> selectFailedRecords(@Param("limit") Integer limit);
    
    /**
     * 根据ID删除记录
     * @param id 记录ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
}
