package com.chenbang.social.service.infrastructure.circuitbreaker;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 熔断器服务
 * <AUTHOR>
 * @since 2024-07-15
 */
@Slf4j
@Service
public class CircuitBreakerService {
    
    private final RedisTemplate<String, Object> redisTemplate;
    
    // 熔断器状态
    private enum CircuitState {
        CLOSED,    // 关闭状态，正常处理请求
        OPEN,      // 开启状态，拒绝所有请求
        HALF_OPEN  // 半开状态，允许少量请求测试
    }
    
    // 熔断器配置
    private static final int FAILURE_THRESHOLD = 10;      // 失败阈值
    private static final int SUCCESS_THRESHOLD = 5;       // 成功阈值（半开状态）
    private static final long TIMEOUT_DURATION = 60;      // 超时时间（秒）
    
    public CircuitBreakerService(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
    
    /**
     * 检查熔断器状态
     */
    public boolean isCircuitOpen(String serviceName) {
        try {
            String stateKey = "circuit:state:" + serviceName;
            String state = (String) redisTemplate.opsForValue().get(stateKey);
            
            if ("OPEN".equals(state)) {
                // 检查是否可以进入半开状态
                String timeoutKey = "circuit:timeout:" + serviceName;
                String timeout = (String) redisTemplate.opsForValue().get(timeoutKey);
                
                if (timeout != null) {
                    long timeoutTime = Long.parseLong(timeout);
                    if (System.currentTimeMillis() > timeoutTime) {
                        // 进入半开状态
                        setCircuitState(serviceName, CircuitState.HALF_OPEN);
                        return false;
                    }
                }
                return true;
            }
            
            return false;
        } catch (Exception e) {
            log.error("检查熔断器状态失败，serviceName: {}", serviceName, e);
            return false;
        }
    }
    
    /**
     * 记录成功调用
     */
    public void recordSuccess(String serviceName) {
        try {
            String stateKey = "circuit:state:" + serviceName;
            String state = (String) redisTemplate.opsForValue().get(stateKey);
            
            if ("HALF_OPEN".equals(state)) {
                // 半开状态下的成功调用
                String successKey = "circuit:success:" + serviceName;
                Long successCount = redisTemplate.opsForValue().increment(successKey);
                redisTemplate.expire(successKey, TIMEOUT_DURATION, TimeUnit.SECONDS);
                
                if (successCount >= SUCCESS_THRESHOLD) {
                    // 成功次数达到阈值，关闭熔断器
                    setCircuitState(serviceName, CircuitState.CLOSED);
                    clearCounters(serviceName);
                }
            } else {
                // 正常状态下清除失败计数
                String failureKey = "circuit:failure:" + serviceName;
                redisTemplate.delete(failureKey);
            }
            
        } catch (Exception e) {
            log.error("记录成功调用失败，serviceName: {}", serviceName, e);
        }
    }
    
    /**
     * 记录失败调用
     */
    public void recordFailure(String serviceName) {
        try {
            String stateKey = "circuit:state:" + serviceName;
            String state = (String) redisTemplate.opsForValue().get(stateKey);
            
            if ("HALF_OPEN".equals(state)) {
                // 半开状态下的失败调用，立即打开熔断器
                setCircuitState(serviceName, CircuitState.OPEN);
                setCircuitTimeout(serviceName);
                clearCounters(serviceName);
            } else if (!"OPEN".equals(state)) {
                // 关闭状态下的失败调用
                String failureKey = "circuit:failure:" + serviceName;
                Long failureCount = redisTemplate.opsForValue().increment(failureKey);
                redisTemplate.expire(failureKey, TIMEOUT_DURATION, TimeUnit.SECONDS);
                
                if (failureCount >= FAILURE_THRESHOLD) {
                    // 失败次数达到阈值，打开熔断器
                    setCircuitState(serviceName, CircuitState.OPEN);
                    setCircuitTimeout(serviceName);
                    clearCounters(serviceName);
                    
                    log.warn("熔断器打开，serviceName: {}, failureCount: {}", serviceName, failureCount);
                }
            }
            
        } catch (Exception e) {
            log.error("记录失败调用失败，serviceName: {}", serviceName, e);
        }
    }
    
    /**
     * 设置熔断器状态
     */
    private void setCircuitState(String serviceName, CircuitState state) {
        String stateKey = "circuit:state:" + serviceName;
        redisTemplate.opsForValue().set(stateKey, state.name(), TIMEOUT_DURATION * 2, TimeUnit.SECONDS);
        
        log.info("熔断器状态变更，serviceName: {}, state: {}", serviceName, state);
    }
    
    /**
     * 设置熔断器超时时间
     */
    private void setCircuitTimeout(String serviceName) {
        String timeoutKey = "circuit:timeout:" + serviceName;
        long timeoutTime = System.currentTimeMillis() + (TIMEOUT_DURATION * 1000);
        redisTemplate.opsForValue().set(timeoutKey, String.valueOf(timeoutTime), TIMEOUT_DURATION * 2, TimeUnit.SECONDS);
    }
    
    /**
     * 清除计数器
     */
    private void clearCounters(String serviceName) {
        String failureKey = "circuit:failure:" + serviceName;
        String successKey = "circuit:success:" + serviceName;
        redisTemplate.delete(failureKey);
        redisTemplate.delete(successKey);
    }
    
    /**
     * 获取熔断器统计信息
     */
    public CircuitBreakerStats getStats(String serviceName) {
        try {
            String stateKey = "circuit:state:" + serviceName;
            String failureKey = "circuit:failure:" + serviceName;
            String successKey = "circuit:success:" + serviceName;
            
            String state = (String) redisTemplate.opsForValue().get(stateKey);
            String failureCount = (String) redisTemplate.opsForValue().get(failureKey);
            String successCount = (String) redisTemplate.opsForValue().get(successKey);
            
            return new CircuitBreakerStats(
                    serviceName,
                    state != null ? state : "CLOSED",
                    failureCount != null ? Integer.parseInt(failureCount) : 0,
                    successCount != null ? Integer.parseInt(successCount) : 0
            );
            
        } catch (Exception e) {
            log.error("获取熔断器统计信息失败，serviceName: {}", serviceName, e);
            return new CircuitBreakerStats(serviceName, "UNKNOWN", 0, 0);
        }
    }
    
    /**
     * 熔断器统计信息
     */
    public static class CircuitBreakerStats {
        private final String serviceName;
        private final String state;
        private final int failureCount;
        private final int successCount;
        
        public CircuitBreakerStats(String serviceName, String state, int failureCount, int successCount) {
            this.serviceName = serviceName;
            this.state = state;
            this.failureCount = failureCount;
            this.successCount = successCount;
        }
        
        // Getters
        public String getServiceName() { return serviceName; }
        public String getState() { return state; }
        public int getFailureCount() { return failureCount; }
        public int getSuccessCount() { return successCount; }
    }
}
