package com.chenbang.social.service.infrastructure.persistence.mapper;

import com.chenbang.social.service.infrastructure.persistence.po.FollowPushConfigPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 关注推送配置Mapper
 * <AUTHOR>
 * @since 2024-07-14
 */
@Mapper
public interface FollowPushConfigMapper {
    
    /**
     * 插入配置
     * @param config 配置信息
     * @return 影响行数
     */
    int insert(FollowPushConfigPO config);
    
    /**
     * 更新配置
     * @param config 配置信息
     * @return 影响行数
     */
    int update(FollowPushConfigPO config);
    
    /**
     * 根据用户ID查询配置
     * @param userId 用户ID
     * @return 配置信息
     */
    FollowPushConfigPO selectByUserId(@Param("userId") Long userId);
    
    /**
     * 根据ID查询配置
     * @param id 配置ID
     * @return 配置信息
     */
    FollowPushConfigPO selectById(@Param("id") Long id);
    
    /**
     * 根据ID删除配置
     * @param id 配置ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
}
