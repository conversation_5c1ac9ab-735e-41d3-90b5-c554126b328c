package com.chenbang.social.service.infrastructure.repository;

import com.chenbang.social.service.domain.entity.TaskBatchError;
import com.chenbang.social.service.domain.repository.TaskBatchErrorRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 任务批次错误仓储实现（内存版本，仅用于演示）
 * 实际项目中应该使用数据库实现
 * <AUTHOR>
 * @since 2024-07-15
 */
@Slf4j
@Repository("taskBatchErrorRepositoryMemoryImpl")
public class TaskBatchErrorRepositoryImpl implements TaskBatchErrorRepository {
    
    private final Map<Long, TaskBatchError> errorById = new ConcurrentHashMap<>();
    private final AtomicLong idGenerator = new AtomicLong(1);
    
    @Override
    public Long save(TaskBatchError error) {
        Long id = idGenerator.getAndIncrement();
        error.setId(id);
        error.setCreateTime(LocalDateTime.now());
        error.setUpdateTime(LocalDateTime.now());
        
        errorById.put(id, error);
        
        log.debug("保存批次错误成功，taskId: {}, id: {}", error.getTaskId(), id);
        return id;
    }
    
    @Override
    public void update(TaskBatchError error) {
        error.setUpdateTime(LocalDateTime.now());
        errorById.put(error.getId(), error);
        
        log.debug("更新批次错误成功，taskId: {}, id: {}", error.getTaskId(), error.getId());
    }
    
    @Override
    public TaskBatchError findById(Long id) {
        return errorById.get(id);
    }
    
    @Override
    public List<TaskBatchError> findByTaskId(String taskId) {
        return errorById.values().stream()
                .filter(error -> taskId.equals(error.getTaskId()))
                .sorted((e1, e2) -> e2.getCreateTime().compareTo(e1.getCreateTime()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<TaskBatchError> findUnfixedErrors(int limit) {
        return errorById.values().stream()
                .filter(error -> !error.getIsFixed())
                .filter(TaskBatchError::canRetry)
                .sorted((e1, e2) -> e1.getCreateTime().compareTo(e2.getCreateTime()))
                .limit(limit)
                .collect(Collectors.toList());
    }
    
    @Override
    public int deleteExpiredErrors(LocalDateTime expireTime) {
        List<TaskBatchError> expiredErrors = errorById.values().stream()
                .filter(error -> error.getCreateTime().isBefore(expireTime))
                .collect(Collectors.toList());
        
        for (TaskBatchError error : expiredErrors) {
            errorById.remove(error.getId());
        }
        
        log.info("清理过期批次错误完成，删除数量: {}", expiredErrors.size());
        return expiredErrors.size();
    }
}
