package com.chenbang.social.service.application;

import com.chenbang.social.service.application.dto.TaskStatistics;

/**
 * 任务监控应用服务接口
 * <AUTHOR>
 * @since 2024-07-15
 */
public interface TaskMonitoringApplicationService {
    
    /**
     * 获取任务统计信息
     * @return 任务统计信息
     */
    TaskStatistics getTaskStatistics();
    
    /**
     * 获取指定发布者的任务统计信息
     * @param publisherId 发布者ID
     * @return 任务统计信息
     */
    TaskStatistics getPublisherTaskStatistics(Long publisherId);
    
    /**
     * 获取系统健康状态
     * @return 是否健康
     */
    boolean isSystemHealthy();
    
    /**
     * 获取当前处理中的任务数
     * @return 处理中的任务数
     */
    int getProcessingTaskCount();
    
    /**
     * 获取当前队列中的任务数
     * @return 队列中的任务数
     */
    int getQueuedTaskCount();
}
