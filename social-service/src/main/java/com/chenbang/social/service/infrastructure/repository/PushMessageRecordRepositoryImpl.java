package com.chenbang.social.service.infrastructure.repository;

import com.chenbang.social.api.enums.PushStatusEnum;
import com.chenbang.social.api.enums.PushTypeEnum;
import com.chenbang.social.service.domain.entity.PushMessageRecord;
import com.chenbang.social.service.domain.repository.PushMessageRecordRepository;
import com.chenbang.social.service.infrastructure.persistence.mapper.PushMessageRecordMapper;
import com.chenbang.social.service.infrastructure.persistence.po.PushMessageRecordPO;
import com.chenbang.social.service.infrastructure.repository.assembler.PushMessageRecordAssembler;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 推送消息记录仓储实现
 * <AUTHOR>
 * @since 2024-07-14
 */
@Repository
@RequiredArgsConstructor
public class PushMessageRecordRepositoryImpl implements PushMessageRecordRepository {

    private final PushMessageRecordMapper pushMessageRecordMapper;
    private final PushMessageRecordAssembler pushMessageRecordAssembler;
    
    @Override
    public Long save(PushMessageRecord record) {
        PushMessageRecordPO po = pushMessageRecordAssembler.convertToPO(record);
        pushMessageRecordMapper.insert(po);
        return po.getId();
    }
    
    @Override
    public void batchSave(List<PushMessageRecord> records) {
        if (records == null || records.isEmpty()) {
            return;
        }
        
        List<PushMessageRecordPO> pos = records.stream()
                .map(pushMessageRecordAssembler::convertToPO)
                .collect(Collectors.toList());
        
        pushMessageRecordMapper.batchInsert(pos);
        
        // 设置生成的ID回到实体对象
        for (int i = 0; i < records.size(); i++) {
            records.get(i).setId(pos.get(i).getId());
        }
    }
    
    @Override
    public void update(PushMessageRecord record) {
        PushMessageRecordPO po = pushMessageRecordAssembler.convertToPO(record);
        pushMessageRecordMapper.update(po);
    }

    @Override
    public PushMessageRecord findById(Long id) {
        PushMessageRecordPO po = pushMessageRecordMapper.selectById(id);
        return pushMessageRecordAssembler.convertToEntity(po);
    }
    
    @Override
    public List<PushMessageRecord> findByUserId(Long userId, Integer pageNum, Integer pageSize) {
        Integer offset = (pageNum - 1) * pageSize;
        List<PushMessageRecordPO> pos = pushMessageRecordMapper.selectByUserId(userId, offset, pageSize);
        
        return pos.stream()
                .map(pushMessageRecordAssembler::convertToEntity)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<PushMessageRecord> findFailedRecords(Integer limit) {
        List<PushMessageRecordPO> pos = pushMessageRecordMapper.selectFailedRecords(limit);
        
        return pos.stream()
                .map(pushMessageRecordAssembler::convertToEntity)
                .collect(Collectors.toList());
    }
    
    @Override
    public void deleteById(Long id) {
        pushMessageRecordMapper.deleteById(id);
    }

}
