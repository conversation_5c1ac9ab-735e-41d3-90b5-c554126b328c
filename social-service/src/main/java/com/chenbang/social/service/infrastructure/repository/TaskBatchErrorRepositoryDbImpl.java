package com.chenbang.social.service.infrastructure.repository;

import com.chenbang.social.service.domain.entity.TaskBatchError;
import com.chenbang.social.service.domain.repository.TaskBatchErrorRepository;
import com.chenbang.social.service.infrastructure.persistence.assembler.TaskBatchErrorAssembler;
import com.chenbang.social.service.infrastructure.persistence.mapper.TaskBatchErrorMapper;
import com.chenbang.social.service.infrastructure.persistence.po.TaskBatchErrorPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务批次错误仓储数据库实现
 * <AUTHOR>
 * @since 2024-07-15
 */
@Slf4j
@Repository
@Primary
@RequiredArgsConstructor
public class TaskBatchErrorRepositoryDbImpl implements TaskBatchErrorRepository {
    
    private final TaskBatchErrorMapper taskBatchErrorMapper;
    private final TaskBatchErrorAssembler taskBatchErrorAssembler;
    
    @Override
    public Long save(TaskBatchError error) {
        TaskBatchErrorPO po = taskBatchErrorAssembler.convertToPO(error);
        po.setCreateTime(LocalDateTime.now());
        po.setUpdateTime(LocalDateTime.now());
        
        int result = taskBatchErrorMapper.insert(po);
        if (result > 0) {
            error.setId(po.getId());
            log.debug("保存批次错误成功，taskId: {}, id: {}", error.getTaskId(), po.getId());
            return po.getId();
        } else {
            log.error("保存批次错误失败，taskId: {}", error.getTaskId());
            throw new RuntimeException("保存批次错误失败");
        }
    }
    
    @Override
    public void update(TaskBatchError error) {
        TaskBatchErrorPO po = taskBatchErrorAssembler.convertToPO(error);
        po.setUpdateTime(LocalDateTime.now());
        
        int result = taskBatchErrorMapper.update(po);
        if (result > 0) {
            log.debug("更新批次错误成功，taskId: {}, id: {}", error.getTaskId(), error.getId());
        } else {
            log.warn("更新批次错误失败，可能记录不存在，taskId: {}, id: {}", error.getTaskId(), error.getId());
        }
    }
    
    @Override
    public TaskBatchError findById(Long id) {
        TaskBatchErrorPO po = taskBatchErrorMapper.selectById(id);
        return taskBatchErrorAssembler.convertToEntity(po);
    }
    
    @Override
    public List<TaskBatchError> findByTaskId(String taskId) {
        List<TaskBatchErrorPO> pos = taskBatchErrorMapper.selectByTaskId(taskId);
        return pos.stream()
                .map(taskBatchErrorAssembler::convertToEntity)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<TaskBatchError> findUnfixedErrors(int limit) {
        List<TaskBatchErrorPO> pos = taskBatchErrorMapper.selectUnfixedErrors(limit);
        return pos.stream()
                .map(taskBatchErrorAssembler::convertToEntity)
                .collect(Collectors.toList());
    }
    
    @Override
    public int deleteExpiredErrors(LocalDateTime expireTime) {
        int deletedCount = taskBatchErrorMapper.deleteExpiredErrors(expireTime);
        log.info("清理过期批次错误完成，删除数量: {}", deletedCount);
        return deletedCount;
    }
}
