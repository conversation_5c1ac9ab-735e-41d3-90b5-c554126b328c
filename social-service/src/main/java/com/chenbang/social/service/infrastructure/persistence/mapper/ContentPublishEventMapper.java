package com.chenbang.social.service.infrastructure.persistence.mapper;

import com.chenbang.social.service.infrastructure.persistence.po.ContentPublishEventPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 内容发布事件Mapper
 * <AUTHOR>
 * @since 2024-07-14
 */
@Mapper
public interface ContentPublishEventMapper {
    
    /**
     * 插入事件
     * @param event 事件信息
     * @return 影响行数
     */
    int insert(ContentPublishEventPO event);
    
    /**
     * 更新事件
     * @param event 事件信息
     * @return 影响行数
     */
    int update(ContentPublishEventPO event);
    
    /**
     * 根据事件ID查询
     * @param eventId 事件ID
     * @return 事件信息
     */
    ContentPublishEventPO selectByEventId(@Param("eventId") String eventId);
    
    /**
     * 根据ID查询事件
     * @param id 主键ID
     * @return 事件信息
     */
    ContentPublishEventPO selectById(@Param("id") Long id);
    
    /**
     * 查询失败的事件（用于重试）
     * @param limit 限制数量
     * @return 事件列表
     */
    List<ContentPublishEventPO> selectFailedEvents(@Param("limit") Integer limit);
    
    /**
     * 根据发布者ID查询事件
     * @param publisherId 发布者ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 事件列表
     */
    List<ContentPublishEventPO> selectByPublisherId(@Param("publisherId") Long publisherId, 
                                                   @Param("offset") Integer offset, 
                                                   @Param("limit") Integer limit);
}
