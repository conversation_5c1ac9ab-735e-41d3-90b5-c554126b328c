package com.chenbang.social.service.infrastructure.persistence.po;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户Feed流持久化对象
 * <AUTHOR>
 * @since 2024-07-14
 */
@Data
public class UserFeedPO {
    
    /**
     * Feed ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 内容发布者ID
     */
    private Long publisherId;
    
    /**
     * 内容ID
     */
    private Long contentId;
    
    /**
     * 内容类型：1-动态，2-文章，3-视频，4-图片，5-音频
     */
    private Integer contentType;
    
    /**
     * 内容标题
     */
    private String contentTitle;
    
    /**
     * 内容摘要
     */
    private String contentSummary;
    
    /**
     * 内容封面图
     */
    private String contentCover;
    
    /**
     * 内容URL
     */
    private String contentUrl;
    
    /**
     * 发布时间
     */
    private LocalDateTime publishTime;
    
    /**
     * 是否已读：0-未读，1-已读
     */
    private Integer readStatus;
    
    /**
     * 租户ID
     */
    private String tenantCode;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建者
     */
    private String createBy;
    
    /**
     * 更新者
     */
    private String updateBy;
    
    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer deleted;
}
