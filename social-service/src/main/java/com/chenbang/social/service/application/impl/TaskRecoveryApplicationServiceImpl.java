package com.chenbang.social.service.application.impl;

import com.chenbang.social.api.enums.TaskStatusEnum;
import com.chenbang.social.service.application.FollowPushApplicationService;
import com.chenbang.social.service.application.TaskRecoveryApplicationService;
import com.chenbang.social.service.domain.entity.ContentPublishTask;
import com.chenbang.social.service.domain.service.FollowPushDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 任务恢复应用服务实现
 * <AUTHOR>
 * @since 2024-07-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskRecoveryApplicationServiceImpl implements TaskRecoveryApplicationService {
    
    private final FollowPushDomainService followPushDomainService;
    private final FollowPushApplicationService followPushApplicationService;
    
    @Override
    @Transactional
    public int recoverAllTasks() {
        log.info("开始恢复所有可恢复的任务");
        
        // 查询所有可恢复的任务
        List<ContentPublishTask> recoverableTasks = followPushDomainService.findRecoverableTasks(100);
        
        int recoveredCount = 0;
        for (ContentPublishTask task : recoverableTasks) {
            try {
                if (recoverSingleTask(task)) {
                    recoveredCount++;
                }
            } catch (Exception e) {
                log.error("恢复任务失败，taskId: {}", task.getTaskId(), e);
                // 标记任务失败
                followPushDomainService.markTaskFailed(task.getTaskId(), "恢复失败: " + e.getMessage());
            }
        }
        
        log.info("任务恢复完成，总计恢复: {} 个任务", recoveredCount);
        return recoveredCount;
    }
    
    @Override
    @Transactional
    public boolean recoverTask(String taskId) {
        log.info("恢复指定任务，taskId: {}", taskId);
        
        ContentPublishTask task = followPushDomainService.getTask(taskId);
        if (task == null) {
            log.warn("任务不存在，taskId: {}", taskId);
            return false;
        }
        
        if (!task.canRecover()) {
            log.warn("任务不可恢复，taskId: {}, status: {}", taskId, task.getTaskStatus());
            return false;
        }
        
        return recoverSingleTask(task);
    }
    
    @Override
    public List<ContentPublishTask> listRecoverableTasks(int limit) {
        log.info("查询可恢复的任务列表，limit: {}", limit);
        return followPushDomainService.findRecoverableTasks(limit);
    }
    
    @Override
    @Transactional
    public boolean cancelTask(String taskId) {
        log.info("取消任务，taskId: {}", taskId);
        
        ContentPublishTask task = followPushDomainService.getTask(taskId);
        if (task == null) {
            log.warn("任务不存在，taskId: {}", taskId);
            return false;
        }
        
        if (task.isProcessed()) {
            log.warn("任务已处理完成，无法取消，taskId: {}, status: {}", taskId, task.getTaskStatus());
            return false;
        }
        
        try {
            task.cancel("system");
            followPushDomainService.markTaskFailed(taskId, "任务已取消");
            log.info("任务取消成功，taskId: {}", taskId);
            return true;
        } catch (Exception e) {
            log.error("取消任务失败，taskId: {}", taskId, e);
            return false;
        }
    }
    
    @Override
    @Transactional
    public boolean retryFailedTask(String taskId) {
        log.info("重试失败的任务，taskId: {}", taskId);
        
        ContentPublishTask task = followPushDomainService.getTask(taskId);
        if (task == null) {
            log.warn("任务不存在，taskId: {}", taskId);
            return false;
        }
        
        if (!task.canRetry()) {
            log.warn("任务不可重试，taskId: {}, status: {}", taskId, task.getTaskStatus());
            return false;
        }
        
        try {
            // 重置任务状态为处理中，但保留进度信息以支持断点恢复
            task.setTaskStatus(TaskStatusEnum.PROCESSING);
            task.setErrorMessage(null);
            // 不重置进度信息，让恢复逻辑从断点继续
            // task.setInAppMessageOffset(0);
            // task.setFeedStreamOffset(0);
            // task.setInAppMessageProgress(0);
            // task.setFeedStreamProgress(0);

            // 保存任务状态更新 - 分别更新站内信和Feed流进度
            if (task.getInAppMessageProgress() != null && task.getInAppMessageOffset() != null) {
                followPushDomainService.updateTaskProgress(taskId, "IN_APP_MESSAGE",
                    task.getInAppMessageProgress(), task.getInAppMessageOffset());
            }
            if (task.getFeedStreamProgress() != null && task.getFeedStreamOffset() != null) {
                followPushDomainService.updateTaskProgress(taskId, "FEED_STREAM",
                    task.getFeedStreamProgress(), task.getFeedStreamOffset());
            }

            // 重新处理任务，handleContentPublish 会检测到可恢复任务并从断点继续
            followPushApplicationService.handleContentPublish(
                    task.getPublisherId(), task.getContentId(), task.getContentType(),
                    task.getContentTitle(), task.getContentSummary(),
                    task.getContentCover(), task.getContentUrl()
            );

            log.info("重试任务成功，taskId: {}", taskId);
            return true;
        } catch (Exception e) {
            log.error("重试任务失败，taskId: {}", taskId, e);
            followPushDomainService.markTaskFailed(taskId, "重试失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 恢复单个任务
     */
    private boolean recoverSingleTask(ContentPublishTask task) {
        try {
            log.info("恢复单个任务，taskId: {}, status: {}", task.getTaskId(), task.getTaskStatus());
            
            // 调用内容发布处理方法，它会自动检测并恢复任务
            followPushApplicationService.handleContentPublish(
                    task.getPublisherId(), task.getContentId(), task.getContentType(),
                    task.getContentTitle(), task.getContentSummary(), 
                    task.getContentCover(), task.getContentUrl()
            );
            
            log.info("恢复任务成功，taskId: {}", task.getTaskId());
            return true;
        } catch (Exception e) {
            log.error("恢复任务失败，taskId: {}", task.getTaskId(), e);
            return false;
        }
    }
}
