package com.chenbang.social.service.infrastructure.cache;

import com.alibaba.fastjson2.JSON;
import com.chenbang.social.service.config.FollowPushProperties;
import com.chenbang.social.service.constants.FollowPushConstants;
import com.chenbang.social.service.domain.entity.FollowPushConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Redis关注推送缓存服务实现
 * <AUTHOR>
 * @since 2024-07-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RedisFollowPushCacheService {
    
    private final RedisTemplate<String, Object> redisTemplate;
    private final FollowPushProperties followPushProperties;
    
    public void cacheFollowPushConfig(Long userId, FollowPushConfig config) {
        try {
            String key = FollowPushConstants.Cache.FOLLOW_PUSH_CONFIG_PREFIX + userId;
            String value = JSON.toJSONString(config);
            
            redisTemplate.opsForValue().set(key, value, 
                    followPushProperties.getPerformance().getCacheExpireHours(), TimeUnit.HOURS);
            
            log.debug("缓存关注推送配置成功，userId: {}", userId);
        } catch (Exception e) {
            log.error("缓存关注推送配置失败，userId: {}", userId, e);
        }
    }
    
    public FollowPushConfig getFollowPushConfig(Long userId) {
        try {
            String key = FollowPushConstants.Cache.FOLLOW_PUSH_CONFIG_PREFIX + userId;
            String value = (String) redisTemplate.opsForValue().get(key);
            
            if (value != null) {
                log.debug("从缓存获取关注推送配置成功，userId: {}", userId);
                return JSON.parseObject(value, FollowPushConfig.class);
            }
        } catch (Exception e) {
            log.error("从缓存获取关注推送配置失败，userId: {}", userId, e);
        }
        return null;
    }
    
    public void removeFollowPushConfig(Long userId) {
        try {
            String key = FollowPushConstants.Cache.FOLLOW_PUSH_CONFIG_PREFIX + userId;
            redisTemplate.delete(key);
            
            log.debug("删除关注推送配置缓存成功，userId: {}", userId);
        } catch (Exception e) {
            log.error("删除关注推送配置缓存失败，userId: {}", userId, e);
        }
    }
    
    /**
     * 缓存关注者列表
     */
    public void cacheFollowers(Long publisherId, String pushType, List<Long> followers) {
        try {
            String key = FollowPushConstants.Cache.FOLLOWERS_PREFIX + publisherId + ":" + pushType;
            String value = JSON.toJSONString(followers);
            
            // 关注者列表缓存时间较短，避免数据不一致
            redisTemplate.opsForValue().set(key, value, 30, TimeUnit.MINUTES);
            
            log.debug("缓存关注者列表成功，publisherId: {}, pushType: {}, size: {}", 
                    publisherId, pushType, followers.size());
        } catch (Exception e) {
            log.error("缓存关注者列表失败，publisherId: {}, pushType: {}", publisherId, pushType, e);
        }
    }
    
    /**
     * 获取缓存的关注者列表
     */
    public List<Long> getCachedFollowers(Long publisherId, String pushType) {
        try {
            String key = FollowPushConstants.Cache.FOLLOWERS_PREFIX + publisherId + ":" + pushType;
            String value = (String) redisTemplate.opsForValue().get(key);
            
            if (value != null) {
                log.debug("从缓存获取关注者列表成功，publisherId: {}, pushType: {}", publisherId, pushType);
                return JSON.parseArray(value, Long.class);
            }
        } catch (Exception e) {
            log.error("从缓存获取关注者列表失败，publisherId: {}, pushType: {}", publisherId, pushType, e);
        }
        return null;
    }
    
    /**
     * 缓存任务处理状态
     */
    public void cacheTaskStatus(String taskId, String status) {
        try {
            String key = "task:status:" + taskId;
            redisTemplate.opsForValue().set(key, status, 1, TimeUnit.HOURS);
            
            log.debug("缓存任务状态成功，taskId: {}, status: {}", taskId, status);
        } catch (Exception e) {
            log.error("缓存任务状态失败，taskId: {}, status: {}", taskId, status, e);
        }
    }
    
    /**
     * 获取缓存的任务状态
     */
    public String getCachedTaskStatus(String taskId) {
        try {
            String key = "task:status:" + taskId;
            String status = (String) redisTemplate.opsForValue().get(key);
            
            if (status != null) {
                log.debug("从缓存获取任务状态成功，taskId: {}, status: {}", taskId, status);
            }
            return status;
        } catch (Exception e) {
            log.error("从缓存获取任务状态失败，taskId: {}", taskId, e);
            return null;
        }
    }
    
    /**
     * 分布式锁 - 防止重复处理
     */
    public boolean tryLock(String lockKey, long expireSeconds) {
        try {
            String key = "lock:" + lockKey;
            Boolean success = redisTemplate.opsForValue().setIfAbsent(key, "1", Duration.ofSeconds(expireSeconds));
            
            if (Boolean.TRUE.equals(success)) {
                log.debug("获取分布式锁成功，lockKey: {}", lockKey);
                return true;
            }
        } catch (Exception e) {
            log.error("获取分布式锁失败，lockKey: {}", lockKey, e);
        }
        return false;
    }
    
    /**
     * 释放分布式锁
     */
    public void releaseLock(String lockKey) {
        try {
            String key = "lock:" + lockKey;
            redisTemplate.delete(key);
            
            log.debug("释放分布式锁成功，lockKey: {}", lockKey);
        } catch (Exception e) {
            log.error("释放分布式锁失败，lockKey: {}", lockKey, e);
        }
    }
    
    /**
     * 批量删除缓存
     */
    public void batchDelete(String pattern) {
        try {
            Set<String> keys = redisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.info("批量删除缓存成功，pattern: {}, count: {}", pattern, keys.size());
            }
        } catch (Exception e) {
            log.error("批量删除缓存失败，pattern: {}", pattern, e);
        }
    }
}
