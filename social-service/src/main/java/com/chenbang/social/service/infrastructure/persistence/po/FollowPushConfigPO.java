package com.chenbang.social.service.infrastructure.persistence.po;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 关注推送配置持久化对象
 * <AUTHOR>
 * @since 2024-07-14
 */
@Data
public class FollowPushConfigPO {
    
    /**
     * 配置ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 推送类型：1-站内信推送，2-Feed流推送，3-两种都推送
     */
    private Integer pushType;
    
    /**
     * 是否启用推送
     */
    private Boolean enabled;
    
    /**
     * 租户ID
     */
    private String tenantCode;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建者
     */
    private String createBy;
    
    /**
     * 更新者
     */
    private String updateBy;
    
    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer deleted;
}
