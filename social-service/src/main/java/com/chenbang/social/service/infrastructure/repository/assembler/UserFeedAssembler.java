package com.chenbang.social.service.infrastructure.repository.assembler;

import com.chenbang.social.api.enums.ContentTypeEnum;
import com.chenbang.social.api.enums.DeleteStatusEnum;
import com.chenbang.social.api.enums.ReadStatusEnum;
import com.chenbang.social.service.domain.entity.UserFeed;
import com.chenbang.social.service.infrastructure.persistence.po.UserFeedPO;
import org.springframework.stereotype.Component;

/**
 * 用户Feed流数据转换器
 * <AUTHOR>
 * @since 2024-07-14
 */
@Component
public class UserFeedAssembler {
    
    /**
     * 领域对象转持久化对象
     * @param feed 领域对象
     * @return 持久化对象
     */
    public UserFeedPO convertToPO(UserFeed feed) {
        if (feed == null) {
            return null;
        }
        
        UserFeedPO po = new UserFeedPO();
        po.setId(feed.getId());
        po.setUserId(feed.getUserId());
        po.setPublisherId(feed.getPublisherId());
        po.setContentId(feed.getContentId());
        po.setContentType(feed.getContentType() != null ? feed.getContentType().getCode() : null);
        po.setContentTitle(feed.getContentTitle());
        po.setContentSummary(feed.getContentSummary());
        po.setContentCover(feed.getContentCover());
        po.setContentUrl(feed.getContentUrl());
        po.setPublishTime(feed.getPublishTime());
        po.setReadStatus(feed.getReadStatus() != null ? feed.getReadStatus().getCode() : null);
        po.setTenantCode(feed.getTenantCode());
        po.setCreateTime(feed.getCreateTime());
        po.setUpdateTime(feed.getUpdateTime());
        po.setCreateBy(feed.getCreateBy());
        po.setUpdateBy(feed.getUpdateBy());
        po.setDeleted(feed.getDeleted() != null ? feed.getDeleted().getCode() : null);
        
        return po;
    }
    
    /**
     * 持久化对象转领域对象
     * @param po 持久化对象
     * @return 领域对象
     */
    public UserFeed convertToEntity(UserFeedPO po) {
        if (po == null) {
            return null;
        }
        
        UserFeed feed = new UserFeed();
        feed.setId(po.getId());
        feed.setUserId(po.getUserId());
        feed.setPublisherId(po.getPublisherId());
        feed.setContentId(po.getContentId());
        feed.setContentType(ContentTypeEnum.getByCode(po.getContentType()));
        feed.setContentTitle(po.getContentTitle());
        feed.setContentSummary(po.getContentSummary());
        feed.setContentCover(po.getContentCover());
        feed.setContentUrl(po.getContentUrl());
        feed.setPublishTime(po.getPublishTime());
        feed.setReadStatus(ReadStatusEnum.getByCode(po.getReadStatus()));
        feed.setTenantCode(po.getTenantCode());
        feed.setCreateTime(po.getCreateTime());
        feed.setUpdateTime(po.getUpdateTime());
        feed.setCreateBy(po.getCreateBy());
        feed.setUpdateBy(po.getUpdateBy());
        feed.setDeleted(DeleteStatusEnum.getByCode(po.getDeleted()));
        
        return feed;
    }
}
