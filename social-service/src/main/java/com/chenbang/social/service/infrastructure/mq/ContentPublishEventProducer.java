package com.chenbang.social.service.infrastructure.mq;

import com.alibaba.fastjson2.JSON;
import com.chenbang.social.api.dto.event.ContentPublishEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

/**
 * 内容发布事件生产者
 * <AUTHOR>
 * @since 2024-07-14
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ContentPublishEventProducer {
    
    private final RocketMQTemplate rocketMQTemplate;
    
    private static final String TOPIC = "CONTENT_PUBLISH_TOPIC";
    private static final String TAG = "CONTENT_PUBLISH";
    
    /**
     * 发送内容发布事件
     * @param event 内容发布事件
     */
    public void sendContentPublishEvent(ContentPublishEvent event) {
        try {
            String message = JSON.toJSONString(event);
            String destination = TOPIC + ":" + TAG;
            
            rocketMQTemplate.convertAndSend(destination, message);
            
            log.info("发送内容发布事件成功，eventId: {}, publisherId: {}, contentId: {}", 
                    event.getEventId(), event.getPublisherId(), event.getContentId());
            
        } catch (Exception e) {
            log.error("发送内容发布事件失败，eventId: {}, publisherId: {}, contentId: {}", 
                    event.getEventId(), event.getPublisherId(), event.getContentId(), e);
            throw e;
        }
    }
    
    /**
     * 发送延迟消息（用于重试）
     * @param event 内容发布事件
     * @param delayLevel 延迟级别
     */
    public void sendDelayedContentPublishEvent(ContentPublishEvent event, int delayLevel) {
        try {
            String messageContent = JSON.toJSONString(event);
            String destination = TOPIC + ":" + TAG;

            rocketMQTemplate.syncSend(destination,
                    MessageBuilder.withPayload(messageContent).build(),
                    3000L, delayLevel);

            log.info("发送延迟内容发布事件成功，eventId: {}, delayLevel: {}", event.getEventId(), delayLevel);

        } catch (Exception e) {
            log.error("发送延迟内容发布事件失败，eventId: {}", event.getEventId(), e);
            throw e;
        }
    }
}
