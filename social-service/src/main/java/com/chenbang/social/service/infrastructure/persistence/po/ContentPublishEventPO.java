package com.chenbang.social.service.infrastructure.persistence.po;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 内容发布事件持久化对象
 * <AUTHOR>
 * @since 2024-07-14
 */
@Data
public class ContentPublishEventPO {
    
    /**
     * 事件ID
     */
    private Long id;
    
    /**
     * 事件唯一标识
     */
    private String eventId;
    
    /**
     * 发布者ID
     */
    private Long publisherId;
    
    /**
     * 内容ID
     */
    private Long contentId;
    
    /**
     * 内容类型：1-动态，2-文章，3-视频，4-图片，5-音频
     */
    private Integer contentType;
    
    /**
     * 内容标题
     */
    private String contentTitle;
    
    /**
     * 内容摘要
     */
    private String contentSummary;
    
    /**
     * 内容封面图
     */
    private String contentCover;
    
    /**
     * 内容URL
     */
    private String contentUrl;
    
    /**
     * 发布时间
     */
    private LocalDateTime publishTime;
    
    /**
     * 处理状态：0-待处理，1-处理中，2-处理成功，3-处理失败
     */
    private Integer processStatus;
    
    /**
     * 处理时间
     */
    private LocalDateTime processTime;
    
    /**
     * 失败原因
     */
    private String failReason;
    
    /**
     * 重试次数
     */
    private Integer retryCount;
    
    /**
     * 租户ID
     */
    private String tenantCode;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
