package com.chenbang.social.service.infrastructure.persistence.po;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 内容发布任务PO
 * <AUTHOR>
 * @since 2024-07-15
 */
@Data
public class ContentPublishTaskPO {
    
    /** 主键ID */
    private Long id;
    
    /** 任务ID */
    private String taskId;
    
    /** 发布者ID */
    private Long publisherId;
    
    /** 内容ID */
    private Long contentId;
    
    /** 内容类型 */
    private Integer contentType;
    
    /** 内容标题 */
    private String contentTitle;
    
    /** 内容摘要 */
    private String contentSummary;
    
    /** 内容封面 */
    private String contentCover;
    
    /** 内容URL */
    private String contentUrl;
    
    /** 任务状态 */
    private String taskStatus;
    
    /** 站内信推送进度 */
    private Integer inAppMessageProgress;

    /** Feed流推送进度 */
    private Integer feedStreamProgress;

    /** 站内信推送偏移量 */
    private Integer inAppMessageOffset;

    /** Feed流推送偏移量 */
    private Integer feedStreamOffset;

    /** 站内信推送总数 */
    private Integer inAppMessageTotal;

    /** Feed流推送总数 */
    private Integer feedStreamTotal;
    
    /** 错误信息 */
    private String errorMessage;
    
    /** 开始处理时间 */
    private LocalDateTime startTime;
    
    /** 完成时间 */
    private LocalDateTime completeTime;
    
    /** 租户编码 */
    private String tenantCode;
    
    /** 创建时间 */
    private LocalDateTime createTime;
    
    /** 更新时间 */
    private LocalDateTime updateTime;
    
    /** 创建人 */
    private String createBy;
    
    /** 更新人 */
    private String updateBy;
}
