package com.chenbang.social.service.infrastructure.persistence.po;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务批次错误记录PO
 * <AUTHOR>
 * @since 2024-07-15
 */
@Data
public class TaskBatchErrorPO {
    
    /** 主键ID */
    private Long id;
    
    /** 任务ID */
    private String taskId;
    
    /** 推送类型：IN_APP_MESSAGE-站内信，FEED_STREAM-Feed流 */
    private String pushType;
    
    /** 批次偏移量 */
    private Integer batchOffset;
    
    /** 错误信息 */
    private String errorMessage;
    
    /** 重试次数 */
    private Integer retryCount;
    
    /** 是否已修复 */
    private Boolean isFixed;
    
    /** 租户编码 */
    private String tenantCode;
    
    /** 创建时间 */
    private LocalDateTime createTime;
    
    /** 更新时间 */
    private LocalDateTime updateTime;
    
    /** 创建人 */
    private String createBy;
    
    /** 更新人 */
    private String updateBy;
}
