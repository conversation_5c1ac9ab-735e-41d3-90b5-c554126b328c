package com.chenbang.social.service.infrastructure.monitoring;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 监控指标服务
 * <AUTHOR>
 * @since 2024-07-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MetricsService {
    
    private final RedisTemplate<String, Object> redisTemplate;
    
    private static final String METRICS_PREFIX = "metrics:";
    private static final DateTimeFormatter HOUR_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd-HH");
    private static final DateTimeFormatter MINUTE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd-HH-mm");
    
    /**
     * 记录任务处理指标
     */
    public void recordTaskMetrics(String taskId, String status, long processingTime) {
        try {
            String hour = LocalDateTime.now().format(HOUR_FORMATTER);
            String minute = LocalDateTime.now().format(MINUTE_FORMATTER);
            
            // 按小时统计
            String hourKey = METRICS_PREFIX + "task:hour:" + hour;
            redisTemplate.opsForHash().increment(hourKey, "total", 1);
            redisTemplate.opsForHash().increment(hourKey, status, 1);
            redisTemplate.opsForHash().increment(hourKey, "total_time", processingTime);
            redisTemplate.expire(hourKey, 7, TimeUnit.DAYS);
            
            // 按分钟统计
            String minuteKey = METRICS_PREFIX + "task:minute:" + minute;
            redisTemplate.opsForHash().increment(minuteKey, "total", 1);
            redisTemplate.opsForHash().increment(minuteKey, status, 1);
            redisTemplate.expire(minuteKey, 24, TimeUnit.HOURS);
            
            // 记录处理时间分布
            recordProcessingTimeDistribution(processingTime);
            
        } catch (Exception e) {
            log.error("记录任务指标失败，taskId: {}, status: {}", taskId, status, e);
        }
    }
    
    /**
     * 记录推送指标
     */
    public void recordPushMetrics(String pushType, int userCount, boolean success) {
        try {
            String hour = LocalDateTime.now().format(HOUR_FORMATTER);
            
            String key = METRICS_PREFIX + "push:hour:" + hour;
            redisTemplate.opsForHash().increment(key, "total_users", userCount);
            redisTemplate.opsForHash().increment(key, pushType + "_users", userCount);
            
            if (success) {
                redisTemplate.opsForHash().increment(key, "success_batches", 1);
                redisTemplate.opsForHash().increment(key, pushType + "_success", 1);
            } else {
                redisTemplate.opsForHash().increment(key, "failed_batches", 1);
                redisTemplate.opsForHash().increment(key, pushType + "_failed", 1);
            }
            
            redisTemplate.expire(key, 7, TimeUnit.DAYS);
            
        } catch (Exception e) {
            log.error("记录推送指标失败，pushType: {}, userCount: {}", pushType, userCount, e);
        }
    }
    
    /**
     * 记录错误指标
     */
    public void recordErrorMetrics(String errorType, String errorMessage) {
        try {
            String hour = LocalDateTime.now().format(HOUR_FORMATTER);
            
            String key = METRICS_PREFIX + "error:hour:" + hour;
            redisTemplate.opsForHash().increment(key, "total", 1);
            redisTemplate.opsForHash().increment(key, errorType, 1);
            redisTemplate.expire(key, 7, TimeUnit.DAYS);
            
            // 记录最近的错误信息
            String recentErrorKey = METRICS_PREFIX + "recent_errors";
            String errorInfo = LocalDateTime.now() + " - " + errorType + ": " + errorMessage;
            redisTemplate.opsForList().leftPush(recentErrorKey, errorInfo);
            redisTemplate.opsForList().trim(recentErrorKey, 0, 99); // 保留最近100条
            redisTemplate.expire(recentErrorKey, 24, TimeUnit.HOURS);
            
        } catch (Exception e) {
            log.error("记录错误指标失败，errorType: {}", errorType, e);
        }
    }
    
    /**
     * 记录系统性能指标
     */
    public void recordSystemMetrics() {
        try {
            String minute = LocalDateTime.now().format(MINUTE_FORMATTER);
            String key = METRICS_PREFIX + "system:minute:" + minute;
            
            // JVM内存使用情况
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            long maxMemory = runtime.maxMemory();
            
            redisTemplate.opsForHash().put(key, "memory_used", usedMemory);
            redisTemplate.opsForHash().put(key, "memory_total", totalMemory);
            redisTemplate.opsForHash().put(key, "memory_max", maxMemory);
            redisTemplate.opsForHash().put(key, "memory_usage_percent", (usedMemory * 100.0) / maxMemory);
            
            // CPU使用情况（简化版）
            redisTemplate.opsForHash().put(key, "cpu_cores", runtime.availableProcessors());
            
            redisTemplate.expire(key, 2, TimeUnit.HOURS);
            
        } catch (Exception e) {
            log.error("记录系统指标失败", e);
        }
    }
    
    /**
     * 记录处理时间分布
     */
    private void recordProcessingTimeDistribution(long processingTime) {
        String hour = LocalDateTime.now().format(HOUR_FORMATTER);
        String key = METRICS_PREFIX + "time_distribution:hour:" + hour;
        
        String bucket;
        if (processingTime < 1000) {
            bucket = "0-1s";
        } else if (processingTime < 5000) {
            bucket = "1-5s";
        } else if (processingTime < 10000) {
            bucket = "5-10s";
        } else if (processingTime < 30000) {
            bucket = "10-30s";
        } else if (processingTime < 60000) {
            bucket = "30-60s";
        } else {
            bucket = "60s+";
        }
        
        redisTemplate.opsForHash().increment(key, bucket, 1);
        redisTemplate.expire(key, 7, TimeUnit.DAYS);
    }
    
    /**
     * 获取任务指标
     */
    public Map<String, Object> getTaskMetrics(String hour) {
        try {
            String key = METRICS_PREFIX + "task:hour:" + hour;
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(key);
            Map<String, Object> result = new HashMap<>();
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                result.put(String.valueOf(entry.getKey()), entry.getValue());
            }
            return result;
        } catch (Exception e) {
            log.error("获取任务指标失败，hour: {}", hour, e);
            return new HashMap<>();
        }
    }
    
    /**
     * 获取推送指标
     */
    public Map<String, Object> getPushMetrics(String hour) {
        try {
            String key = METRICS_PREFIX + "push:hour:" + hour;
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(key);
            Map<String, Object> result = new HashMap<>();
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                result.put(String.valueOf(entry.getKey()), entry.getValue());
            }
            return result;
        } catch (Exception e) {
            log.error("获取推送指标失败，hour: {}", hour, e);
            return new HashMap<>();
        }
    }
    
    /**
     * 获取错误指标
     */
    public Map<String, Object> getErrorMetrics(String hour) {
        try {
            String key = METRICS_PREFIX + "error:hour:" + hour;
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(key);
            Map<String, Object> result = new HashMap<>();
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                result.put(String.valueOf(entry.getKey()), entry.getValue());
            }
            return result;
        } catch (Exception e) {
            log.error("获取错误指标失败，hour: {}", hour, e);
            return new HashMap<>();
        }
    }
    
    /**
     * 获取系统指标
     */
    public Map<String, Object> getSystemMetrics(String minute) {
        try {
            String key = METRICS_PREFIX + "system:minute:" + minute;
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(key);
            Map<String, Object> result = new HashMap<>();
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                result.put(String.valueOf(entry.getKey()), entry.getValue());
            }
            return result;
        } catch (Exception e) {
            log.error("获取系统指标失败，minute: {}", minute, e);
            return new HashMap<>();
        }
    }
    
    /**
     * 获取最近错误信息
     */
    public java.util.List<Object> getRecentErrors() {
        try {
            String key = METRICS_PREFIX + "recent_errors";
            return redisTemplate.opsForList().range(key, 0, 49); // 获取最近50条
        } catch (Exception e) {
            log.error("获取最近错误信息失败", e);
            return new java.util.ArrayList<>();
        }
    }
    
    /**
     * 检查告警条件
     */
    public void checkAlerts() {
        try {
            String currentHour = LocalDateTime.now().format(HOUR_FORMATTER);
            
            // 检查任务失败率
            Map<String, Object> taskMetrics = getTaskMetrics(currentHour);
            checkTaskFailureRate(taskMetrics);
            
            // 检查系统内存使用率
            String currentMinute = LocalDateTime.now().format(MINUTE_FORMATTER);
            Map<String, Object> systemMetrics = getSystemMetrics(currentMinute);
            checkMemoryUsage(systemMetrics);
            
        } catch (Exception e) {
            log.error("检查告警条件失败", e);
        }
    }
    
    /**
     * 检查任务失败率
     */
    private void checkTaskFailureRate(Map<String, Object> metrics) {
        if (metrics.isEmpty()) return;
        
        Object totalObj = metrics.get("total");
        Object failedObj = metrics.get("FAILED");
        
        if (totalObj != null && failedObj != null) {
            long total = Long.parseLong(totalObj.toString());
            long failed = Long.parseLong(failedObj.toString());
            
            if (total > 0) {
                double failureRate = (failed * 100.0) / total;
                if (failureRate > 20.0) { // 失败率超过20%
                    log.error("【告警】任务失败率过高: {}%, 总数: {}, 失败数: {}", failureRate, total, failed);
                    // TODO: 发送告警通知
                }
            }
        }
    }
    
    /**
     * 检查内存使用率
     */
    private void checkMemoryUsage(Map<String, Object> metrics) {
        if (metrics.isEmpty()) return;
        
        Object usageObj = metrics.get("memory_usage_percent");
        if (usageObj != null) {
            double usage = Double.parseDouble(usageObj.toString());
            if (usage > 80.0) { // 内存使用率超过80%
                log.error("【告警】内存使用率过高: {}%", usage);
                // TODO: 发送告警通知
            }
        }
    }
}
