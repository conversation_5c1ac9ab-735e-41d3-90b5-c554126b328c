package com.chenbang.social.service.infrastructure.cache;

import com.alibaba.fastjson2.JSON;
import com.chenbang.social.service.domain.entity.FollowPushConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 关注推送缓存服务
 * <AUTHOR>
 * @since 2024-07-14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FollowPushCacheService {
    
    private final RedisTemplate<String, String> redisTemplate;
    
    private static final String FOLLOW_PUSH_CONFIG_KEY = "follow:push:config:";
    private static final String FOLLOWERS_KEY = "follow:followers:";
    private static final String USER_FEED_KEY = "user:feed:";
    
    private static final int CACHE_EXPIRE_HOURS = 2;
    
    /**
     * 缓存用户关注推送配置
     * @param userId 用户ID
     * @param config 配置信息
     */
    public void cacheFollowPushConfig(Long userId, FollowPushConfig config) {
        try {
            String key = FOLLOW_PUSH_CONFIG_KEY + userId;
            String value = JSON.toJSONString(config);
            redisTemplate.opsForValue().set(key, value, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
            
            log.debug("缓存用户关注推送配置成功，userId: {}", userId);
        } catch (Exception e) {
            log.error("缓存用户关注推送配置失败，userId: {}", userId, e);
        }
    }
    
    /**
     * 获取用户关注推送配置
     * @param userId 用户ID
     * @return 配置信息
     */
    public FollowPushConfig getFollowPushConfig(Long userId) {
        try {
            String key = FOLLOW_PUSH_CONFIG_KEY + userId;
            String value = redisTemplate.opsForValue().get(key);
            
            if (value != null) {
                log.debug("从缓存获取用户关注推送配置成功，userId: {}", userId);
                return JSON.parseObject(value, FollowPushConfig.class);
            }
        } catch (Exception e) {
            log.error("从缓存获取用户关注推送配置失败，userId: {}", userId, e);
        }
        return null;
    }
    
    /**
     * 删除用户关注推送配置缓存
     * @param userId 用户ID
     */
    public void deleteFollowPushConfig(Long userId) {
        try {
            String key = FOLLOW_PUSH_CONFIG_KEY + userId;
            redisTemplate.delete(key);
            
            log.debug("删除用户关注推送配置缓存成功，userId: {}", userId);
        } catch (Exception e) {
            log.error("删除用户关注推送配置缓存失败，userId: {}", userId, e);
        }
    }
    
    /**
     * 缓存用户关注者列表
     * @param publisherId 发布者ID
     * @param followers 关注者列表
     */
    public void cacheFollowers(Long publisherId, List<Long> followers) {
        try {
            String key = FOLLOWERS_KEY + publisherId;
            String value = JSON.toJSONString(followers);
            redisTemplate.opsForValue().set(key, value, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
            
            log.debug("缓存用户关注者列表成功，publisherId: {}, size: {}", publisherId, followers.size());
        } catch (Exception e) {
            log.error("缓存用户关注者列表失败，publisherId: {}", publisherId, e);
        }
    }
    
    /**
     * 获取用户关注者列表
     * @param publisherId 发布者ID
     * @return 关注者列表
     */
    @SuppressWarnings("unchecked")
    public List<Long> getFollowers(Long publisherId) {
        try {
            String key = FOLLOWERS_KEY + publisherId;
            String value = redisTemplate.opsForValue().get(key);
            
            if (value != null) {
                log.debug("从缓存获取用户关注者列表成功，publisherId: {}", publisherId);
                return JSON.parseArray(value, Long.class);
            }
        } catch (Exception e) {
            log.error("从缓存获取用户关注者列表失败，publisherId: {}", publisherId, e);
        }
        return null;
    }
    
    /**
     * 删除用户关注者列表缓存
     * @param publisherId 发布者ID
     */
    public void deleteFollowers(Long publisherId) {
        try {
            String key = FOLLOWERS_KEY + publisherId;
            redisTemplate.delete(key);
            
            log.debug("删除用户关注者列表缓存成功，publisherId: {}", publisherId);
        } catch (Exception e) {
            log.error("删除用户关注者列表缓存失败，publisherId: {}", publisherId, e);
        }
    }
    
    /**
     * 缓存用户Feed流数量
     * @param userId 用户ID
     * @param count 数量
     */
    public void cacheUserFeedCount(Long userId, Integer count) {
        try {
            String key = USER_FEED_KEY + "count:" + userId;
            redisTemplate.opsForValue().set(key, count.toString(), CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
            
            log.debug("缓存用户Feed流数量成功，userId: {}, count: {}", userId, count);
        } catch (Exception e) {
            log.error("缓存用户Feed流数量失败，userId: {}", userId, e);
        }
    }
    
    /**
     * 获取用户Feed流数量
     * @param userId 用户ID
     * @return 数量
     */
    public Integer getUserFeedCount(Long userId) {
        try {
            String key = USER_FEED_KEY + "count:" + userId;
            String value = redisTemplate.opsForValue().get(key);
            
            if (value != null) {
                log.debug("从缓存获取用户Feed流数量成功，userId: {}", userId);
                return Integer.parseInt(value);
            }
        } catch (Exception e) {
            log.error("从缓存获取用户Feed流数量失败，userId: {}", userId, e);
        }
        return null;
    }
}
