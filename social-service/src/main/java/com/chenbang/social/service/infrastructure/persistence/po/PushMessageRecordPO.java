package com.chenbang.social.service.infrastructure.persistence.po;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 推送消息记录持久化对象
 * <AUTHOR>
 * @since 2024-07-14
 */
@Data
public class PushMessageRecordPO {
    
    /**
     * 记录ID
     */
    private Long id;
    
    /**
     * 用户ID（接收者）
     */
    private Long userId;
    
    /**
     * 发布者ID
     */
    private Long publisherId;
    
    /**
     * 内容ID
     */
    private Long contentId;
    
    /**
     * 推送类型：1-站内信推送，2-Feed流推送
     */
    private Integer pushType;
    
    /**
     * 推送状态：0-待推送，1-推送成功，2-推送失败，3-推送中，4-已取消
     */
    private Integer pushStatus;
    
    /**
     * 消息标题
     */
    private String messageTitle;
    
    /**
     * 消息内容
     */
    private String messageContent;
    
    /**
     * 推送时间
     */
    private LocalDateTime pushTime;
    
    /**
     * 失败原因
     */
    private String failReason;
    
    /**
     * 重试次数
     */
    private Integer retryCount;
    
    /**
     * 租户ID
     */
    private String tenantCode;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建者
     */
    private String createBy;
    
    /**
     * 更新者
     */
    private String updateBy;
    
    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer deleted;
}
