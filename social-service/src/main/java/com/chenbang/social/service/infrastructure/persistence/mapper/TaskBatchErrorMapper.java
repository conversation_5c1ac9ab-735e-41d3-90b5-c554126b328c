package com.chenbang.social.service.infrastructure.persistence.mapper;

import com.chenbang.social.service.infrastructure.persistence.po.TaskBatchErrorPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务批次错误Mapper
 * <AUTHOR>
 * @since 2024-07-15
 */
@Mapper
public interface TaskBatchErrorMapper {
    
    /**
     * 插入批次错误
     * @param error 批次错误PO
     * @return 影响行数
     */
    int insert(TaskBatchErrorPO error);
    
    /**
     * 更新批次错误
     * @param error 批次错误PO
     * @return 影响行数
     */
    int update(TaskBatchErrorPO error);
    
    /**
     * 根据ID查询批次错误
     * @param id 主键ID
     * @return 批次错误PO
     */
    TaskBatchErrorPO selectById(@Param("id") Long id);
    
    /**
     * 根据任务ID查询批次错误列表
     * @param taskId 任务ID
     * @return 批次错误列表
     */
    List<TaskBatchErrorPO> selectByTaskId(@Param("taskId") String taskId);
    
    /**
     * 查询未修复的批次错误列表
     * @param limit 限制数量
     * @return 批次错误列表
     */
    List<TaskBatchErrorPO> selectUnfixedErrors(@Param("limit") int limit);
    
    /**
     * 删除过期批次错误
     * @param expireTime 过期时间
     * @return 删除数量
     */
    int deleteExpiredErrors(@Param("expireTime") LocalDateTime expireTime);
    
    /**
     * 根据ID删除批次错误
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
}
