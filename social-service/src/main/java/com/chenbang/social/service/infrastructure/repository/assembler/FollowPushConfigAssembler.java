package com.chenbang.social.service.infrastructure.repository.assembler;

import com.chenbang.social.api.enums.PushTypeEnum;
import com.chenbang.social.service.domain.entity.FollowPushConfig;
import com.chenbang.social.service.infrastructure.persistence.po.FollowPushConfigPO;
import org.springframework.stereotype.Component;

/**
 * 关注推送配置数据转换器
 * <AUTHOR>
 * @since 2024-07-14
 */
@Component
public class FollowPushConfigAssembler {
    
    /**
     * 领域对象转持久化对象
     * @param config 领域对象
     * @return 持久化对象
     */
    public FollowPushConfigPO convertToPO(FollowPushConfig config) {
        if (config == null) {
            return null;
        }
        
        FollowPushConfigPO po = new FollowPushConfigPO();
        po.setId(config.getId());
        po.setUserId(config.getUserId());
        po.setPushType(config.getPushType() != null ? config.getPushType().getCode() : null);
        po.setEnabled(config.getEnabled());
        po.setTenantCode(config.getTenantCode());
        po.setCreateTime(config.getCreateTime());
        po.setUpdateTime(config.getUpdateTime());
        po.setCreateBy(config.getCreateBy());
        po.setUpdateBy(config.getUpdateBy());
        po.setDeleted(config.getDeleted());
        
        return po;
    }
    
    /**
     * 持久化对象转领域对象
     * @param po 持久化对象
     * @return 领域对象
     */
    public FollowPushConfig convertToEntity(FollowPushConfigPO po) {
        if (po == null) {
            return null;
        }
        
        FollowPushConfig config = new FollowPushConfig();
        config.setId(po.getId());
        config.setUserId(po.getUserId());
        config.setPushType(PushTypeEnum.getByCode(po.getPushType()));
        config.setEnabled(po.getEnabled());
        config.setTenantCode(po.getTenantCode());
        config.setCreateTime(po.getCreateTime());
        config.setUpdateTime(po.getUpdateTime());
        config.setCreateBy(po.getCreateBy());
        config.setUpdateBy(po.getUpdateBy());
        config.setDeleted(po.getDeleted());
        
        return config;
    }
}
