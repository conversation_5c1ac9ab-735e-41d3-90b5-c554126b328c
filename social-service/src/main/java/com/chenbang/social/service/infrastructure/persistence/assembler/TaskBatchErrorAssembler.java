package com.chenbang.social.service.infrastructure.persistence.assembler;

import com.chenbang.social.service.domain.entity.TaskBatchError;
import com.chenbang.social.service.infrastructure.persistence.po.TaskBatchErrorPO;
import org.springframework.stereotype.Component;

/**
 * 任务批次错误转换器
 * <AUTHOR>
 * @since 2024-07-15
 */
@Component
public class TaskBatchErrorAssembler {
    
    /**
     * 实体转PO
     */
    public TaskBatchErrorPO convertToPO(TaskBatchError entity) {
        if (entity == null) {
            return null;
        }
        
        TaskBatchErrorPO po = new TaskBatchErrorPO();
        po.setId(entity.getId());
        po.setTaskId(entity.getTaskId());
        po.setPushType(entity.getPushType());
        po.setBatchOffset(entity.getBatchOffset());
        po.setErrorMessage(entity.getErrorMessage());
        po.setRetryCount(entity.getRetryCount());
        po.setIsFixed(entity.getIsFixed());
        po.setTenantCode(entity.getTenantCode());
        po.setCreateTime(entity.getCreateTime());
        po.setUpdateTime(entity.getUpdateTime());
        po.setCreateBy(entity.getCreateBy());
        po.setUpdateBy(entity.getUpdateBy());
        
        return po;
    }
    
    /**
     * PO转实体
     */
    public TaskBatchError convertToEntity(TaskBatchErrorPO po) {
        if (po == null) {
            return null;
        }
        
        TaskBatchError entity = new TaskBatchError();
        entity.setId(po.getId());
        entity.setTaskId(po.getTaskId());
        entity.setPushType(po.getPushType());
        entity.setBatchOffset(po.getBatchOffset());
        entity.setErrorMessage(po.getErrorMessage());
        entity.setRetryCount(po.getRetryCount());
        entity.setIsFixed(po.getIsFixed());
        entity.setTenantCode(po.getTenantCode());
        entity.setCreateTime(po.getCreateTime());
        entity.setUpdateTime(po.getUpdateTime());
        entity.setCreateBy(po.getCreateBy());
        entity.setUpdateBy(po.getUpdateBy());
        
        return entity;
    }
}
