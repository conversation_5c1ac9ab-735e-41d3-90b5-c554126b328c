package com.chenbang.social.service.infrastructure.repository;

import com.chenbang.social.api.enums.ContentTypeEnum;
import com.chenbang.social.service.domain.entity.UserFeed;
import com.chenbang.social.service.domain.repository.UserFeedRepository;
import com.chenbang.social.service.infrastructure.persistence.mapper.UserFeedMapper;
import com.chenbang.social.service.infrastructure.persistence.po.UserFeedPO;
import com.chenbang.social.service.infrastructure.repository.assembler.UserFeedAssembler;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户Feed流仓储实现
 * <AUTHOR>
 * @since 2024-07-14
 */
@Repository
@RequiredArgsConstructor
public class UserFeedRepositoryImpl implements UserFeedRepository {

    private final UserFeedMapper userFeedMapper;
    private final UserFeedAssembler userFeedAssembler;
    
    @Override
    public Long save(UserFeed feed) {
        UserFeedPO po = userFeedAssembler.convertToPO(feed);
        userFeedMapper.insert(po);
        return po.getId();
    }

    @Override
    public void batchSave(List<UserFeed> feeds) {
        if (feeds == null || feeds.isEmpty()) {
            return;
        }

        List<UserFeedPO> pos = feeds.stream()
                .map(userFeedAssembler::convertToPO)
                .collect(Collectors.toList());

        userFeedMapper.batchInsert(pos);

        // 设置生成的ID回到实体对象
        for (int i = 0; i < feeds.size(); i++) {
            feeds.get(i).setId(pos.get(i).getId());
        }
    }

    @Override
    public void update(UserFeed feed) {
        UserFeedPO po = userFeedAssembler.convertToPO(feed);
        userFeedMapper.update(po);
    }

    @Override
    public UserFeed findById(Long id) {
        UserFeedPO po = userFeedMapper.selectById(id);
        return userFeedAssembler.convertToEntity(po);
    }

    @Override
    public List<UserFeed> findByUserIdWithTimeline(Long userId, Long lastTimestamp, Integer pageSize, ContentTypeEnum contentType) {
        Integer contentTypeCode = contentType != null ? contentType.getCode() : null;
        List<UserFeedPO> pos = userFeedMapper.selectByUserIdWithTimeline(userId, lastTimestamp, pageSize, contentTypeCode);

        return pos.stream()
                .map(userFeedAssembler::convertToEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<UserFeed> findByUserId(Long userId, Integer pageNum, Integer pageSize, ContentTypeEnum contentType) {
        Integer offset = (pageNum - 1) * pageSize;
        Integer contentTypeCode = contentType != null ? contentType.getCode() : null;
        List<UserFeedPO> pos = userFeedMapper.selectByUserId(userId, offset, pageSize, contentTypeCode);

        return pos.stream()
                .map(userFeedAssembler::convertToEntity)
                .collect(Collectors.toList());
    }
    
    @Override
    public Integer deleteExpiredFeeds(LocalDateTime expireTime) {
        return userFeedMapper.deleteExpiredFeeds(expireTime);
    }
    
    @Override
    public void deleteById(Long id) {
        userFeedMapper.deleteById(id);
    }

}
