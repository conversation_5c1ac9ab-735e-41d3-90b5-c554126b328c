package com.chenbang.social.service.infrastructure.mq;

import com.alibaba.fastjson2.JSON;
import com.chenbang.social.api.dto.event.ContentPublishEvent;
import com.chenbang.social.api.enums.ContentTypeEnum;
import com.chenbang.social.service.application.FollowPushApplicationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

/**
 * 内容发布事件消费者
 * <AUTHOR>
 * @since 2024-07-14
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
    topic = "CONTENT_PUBLISH_TOPIC",
    consumerGroup = "social-follow-push-consumer-group",
    selectorExpression = "CONTENT_PUBLISH"
)
public class ContentPublishEventConsumer implements RocketMQListener<String> {
    
    private final FollowPushApplicationService followPushApplicationService;
    
    @Override
    public void onMessage(String message) {
        log.info("接收到内容发布事件消息: {}", message);

        ContentPublishEvent event = null;
        try {
            event = JSON.parseObject(message, ContentPublishEvent.class);

            // 参数校验
            if (event == null || event.getPublisherId() == null || event.getContentId() == null) {
                log.warn("内容发布事件参数无效，跳过处理，message: {}", message);
                return; // 直接返回，不重试
            }

            log.info("开始处理内容发布事件，eventId: {}, publisherId: {}, contentId: {}",
                    event.getEventId(), event.getPublisherId(), event.getContentId());

            // 处理内容发布事件，触发关注推送
            // 新的实现已经包含幂等性控制，重复消费不会重复处理
            followPushApplicationService.handleContentPublish(
                event.getPublisherId(),
                event.getContentId(),
                event.getContentType(),
                event.getContentTitle(),
                event.getContentSummary(),
                event.getContentCover(),
                event.getContentUrl()
            );

            log.info("内容发布事件处理成功，eventId: {}, publisherId: {}, contentId: {}",
                    event.getEventId(), event.getPublisherId(), event.getContentId());

        } catch (IllegalArgumentException e) {
            // 参数错误，不重试
            log.error("内容发布事件参数错误，不重试，message: {}", message, e);

        } catch (Exception e) {
            log.error("处理内容发布事件失败，eventId: {}, publisherId: {}, contentId: {}, message: {}",
                    event != null ? event.getEventId() : "unknown",
                    event != null ? event.getPublisherId() : "unknown",
                    event != null ? event.getContentId() : "unknown",
                    message, e);

            // 重新抛出异常，触发消息重试
            // 由于新实现有幂等性控制，重试时不会重复处理已成功的部分
            throw e;
        }
    }
}
