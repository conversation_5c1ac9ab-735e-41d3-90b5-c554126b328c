package com.chenbang.social.service.infrastructure.repository;

import com.chenbang.social.service.domain.entity.FollowPushConfig;
import com.chenbang.social.service.domain.repository.FollowPushConfigRepository;
import com.chenbang.social.service.infrastructure.persistence.mapper.FollowPushConfigMapper;
import com.chenbang.social.service.infrastructure.persistence.po.FollowPushConfigPO;
import com.chenbang.social.service.infrastructure.repository.assembler.FollowPushConfigAssembler;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * 关注推送配置仓储实现
 * <AUTHOR>
 * @since 2024-07-14
 */
@Repository
@RequiredArgsConstructor
public class FollowPushConfigRepositoryImpl implements FollowPushConfigRepository {

    private final FollowPushConfigMapper followPushConfigMapper;
    private final FollowPushConfigAssembler followPushConfigAssembler;
    
    @Override
    public Long save(FollowPushConfig config) {
        FollowPushConfigPO po = followPushConfigAssembler.convertToPO(config);
        followPushConfigMapper.insert(po);
        return po.getId();
    }

    @Override
    public void update(FollowPushConfig config) {
        FollowPushConfigPO po = followPushConfigAssembler.convertToPO(config);
        followPushConfigMapper.update(po);
    }

    @Override
    public FollowPushConfig findByUserId(Long userId) {
        FollowPushConfigPO po = followPushConfigMapper.selectByUserId(userId);
        return followPushConfigAssembler.convertToEntity(po);
    }

    @Override
    public FollowPushConfig findById(Long id) {
        FollowPushConfigPO po = followPushConfigMapper.selectById(id);
        return followPushConfigAssembler.convertToEntity(po);
    }
    
    @Override
    public void deleteById(Long id) {
        followPushConfigMapper.deleteById(id);
    }

}
