package com.chenbang.social.service.infrastructure.job;

import com.chenbang.social.service.application.FeedApplicationService;
import com.chenbang.social.service.application.MessagePushApplicationService;
import com.chenbang.social.service.application.TaskManagementApplicationService;
import com.chenbang.social.service.application.TaskMonitoringApplicationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 关注推送定时任务处理器
 * <AUTHOR>
 * @since 2024-07-14
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FollowPushJobHandler {
    
    private final FeedApplicationService feedApplicationService;
    private final MessagePushApplicationService messagePushApplicationService;
    private final TaskManagementApplicationService taskManagementApplicationService;
    private final TaskMonitoringApplicationService taskMonitoringApplicationService;
    
    /**
     * 清理过期的Feed流记录
     * 每天凌晨2点执行，清理30天前的记录
     */
    public void cleanExpiredFeedJob() {
        log.info("开始执行清理过期Feed流记录任务");
        
        try {
            feedApplicationService.cleanExpiredFeed(30);
            log.info("清理过期Feed流记录任务执行成功");
        } catch (Exception e) {
            log.error("清理过期Feed流记录任务执行失败", e);
            throw e;
        }
    }
    
    /**
     * 重试失败的推送消息
     * 每5分钟执行一次
     */
    public void retryFailedPushMessagesJob() {
        log.info("开始执行重试失败推送消息任务");
        
        try {
            messagePushApplicationService.scheduleRetryFailedMessages();
            log.info("重试失败推送消息任务执行成功");
        } catch (Exception e) {
            log.error("重试失败推送消息任务执行失败", e);
            throw e;
        }
    }

    /**
     * 清理过期任务记录
     * 每天凌晨3点执行
     */
    public void cleanExpiredTasksJob() {
        log.info("开始执行清理过期任务记录任务");

        try {
            int deletedCount = taskManagementApplicationService.cleanExpiredTasks(7); // 保留7天
            log.info("清理过期任务记录任务执行成功，删除数量: {}", deletedCount);
        } catch (Exception e) {
            log.error("清理过期任务记录任务执行失败", e);
            throw e;
        }
    }

    /**
     * 任务监控统计
     * 每分钟执行一次
     */
    public void taskMonitoringJob() {
        log.debug("开始执行任务监控统计");

        try {
            // 检查系统健康状态
            boolean isHealthy = taskMonitoringApplicationService.isSystemHealthy();
            if (!isHealthy) {
                log.warn("系统健康检查失败，请检查系统状态");
            }

            // 记录关键指标
            int processingCount = taskMonitoringApplicationService.getProcessingTaskCount();
            int queuedCount = taskMonitoringApplicationService.getQueuedTaskCount();

            log.info("任务监控统计 - 处理中任务: {}, 队列中任务: {}, 系统健康: {}",
                    processingCount, queuedCount, isHealthy);

        } catch (Exception e) {
            log.error("任务监控统计执行失败", e);
            // 监控任务失败不抛出异常，避免影响其他任务
        }
    }
}
