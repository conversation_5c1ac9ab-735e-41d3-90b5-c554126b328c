# 关注推送系统配置

# RocketMQ配置
rocketmq:
  name-server: localhost:9876
  producer:
    group: social-follow-push-producer-group
    send-message-timeout: 3000
    retry-times-when-send-failed: 2
  consumer:
    group: social-follow-push-consumer-group
    consume-timeout: 15000
    max-reconsume-times: 3

# 关注推送业务配置
social:
  follow-push:
    # Feed流配置
    feed:
      # Feed流记录保留天数
      retention-days: 30
      # 单次查询最大数量
      max-page-size: 100
      # 默认页大小
      default-page-size: 20
    
    # 推送消息配置
    push:
      # 最大重试次数
      max-retry-count: 3
      # 重试间隔（分钟）
      retry-interval: 5
      # 批量推送大小
      batch-size: 1000
      # 批次处理间隔（毫秒）
      batch-interval: 100
      # 任务超时时间（分钟）
      task-timeout: 60
    
    # 定时任务配置
    job:
      # 清理过期Feed流记录的cron表达式（每天凌晨2点）
      clean-expired-feed-cron: "0 0 2 * * ?"
      # 重试失败推送消息的cron表达式（每5分钟）
      retry-failed-push-cron: "0 */5 * * * ?"
      # 清理过期任务记录的cron表达式（每天凌晨3点）
      clean-expired-task-cron: "0 0 3 * * ?"
      # 任务监控统计的cron表达式（每分钟）
      task-monitoring-cron: "0 * * * * ?"

    # 任务管理配置
    task:
      # 任务记录保留天数
      retention-days: 7
      # 批次错误记录保留天数
      error-retention-days: 30

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/follow-push/*.xml
  type-aliases-package: com.chenbang.social.service.infrastructure.persistence.po
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
    use-column-label: true
    use-generated-keys: true
    auto-mapping-behavior: partial
    default-executor-type: simple
    default-statement-timeout: 25000
