-- 内容发布任务表
CREATE TABLE content_publish_task (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    task_id VARCHAR(128) NOT NULL UNIQUE COMMENT '任务ID',
    publisher_id BIGINT NOT NULL COMMENT '发布者ID',
    content_id BIGINT NOT NULL COMMENT '内容ID',
    content_type TINYINT NOT NULL COMMENT '内容类型：1-动态，2-文章，3-视频',
    content_title VARCHAR(500) COMMENT '内容标题',
    content_summary TEXT COMMENT '内容摘要',
    content_cover VARCHAR(500) COMMENT '内容封面',
    content_url VARCHAR(500) COMMENT '内容URL',
    task_status VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '任务状态：PENDING-待处理，PROCESSING-处理中，COMPLETED-已完成，FAILED-失败，CANCELLED-已取消',
    in_app_message_progress INT DEFAULT 0 COMMENT '站内信推送进度',
    feed_stream_progress INT DEFAULT 0 COMMENT 'Feed流推送进度',
    error_message TEXT COMMENT '错误信息',
    start_time DATETIME COMMENT '开始处理时间',
    complete_time DATETIME COMMENT '完成时间',
    tenant_code VARCHAR(50) NOT NULL DEFAULT 'default' COMMENT '租户编码',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
    update_by VARCHAR(50) NOT NULL DEFAULT 'system' COMMENT '更新人',
    
    INDEX idx_task_id (task_id),
    INDEX idx_publisher_content (publisher_id, content_id),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='内容发布任务表';

-- 任务批次错误表
CREATE TABLE task_batch_error (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    task_id VARCHAR(128) NOT NULL COMMENT '任务ID',
    push_type VARCHAR(20) NOT NULL COMMENT '推送类型：IN_APP_MESSAGE-站内信，FEED_STREAM-Feed流',
    batch_offset INT NOT NULL COMMENT '批次偏移量',
    error_message TEXT COMMENT '错误信息',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    is_fixed BOOLEAN DEFAULT FALSE COMMENT '是否已修复',
    tenant_code VARCHAR(50) NOT NULL DEFAULT 'default' COMMENT '租户编码',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
    update_by VARCHAR(50) NOT NULL DEFAULT 'system' COMMENT '更新人',
    
    INDEX idx_task_id (task_id),
    INDEX idx_unfixed_retry (is_fixed, retry_count),
    INDEX idx_tenant_create_time (tenant_code, create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务批次错误表';

-- 任务统计视图
CREATE VIEW v_task_statistics AS
SELECT 
    COUNT(*) as total_tasks,
    SUM(CASE WHEN task_status = 'PENDING' THEN 1 ELSE 0 END) as pending_tasks,
    SUM(CASE WHEN task_status = 'PROCESSING' THEN 1 ELSE 0 END) as processing_tasks,
    SUM(CASE WHEN task_status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_tasks,
    SUM(CASE WHEN task_status = 'FAILED' THEN 1 ELSE 0 END) as failed_tasks,
    SUM(CASE WHEN task_status = 'CANCELLED' THEN 1 ELSE 0 END) as cancelled_tasks,
    AVG(CASE 
        WHEN task_status = 'COMPLETED' AND start_time IS NOT NULL AND complete_time IS NOT NULL 
        THEN TIMESTAMPDIFF(SECOND, start_time, complete_time) 
        ELSE NULL 
    END) as avg_processing_time,
    MAX(CASE 
        WHEN task_status = 'COMPLETED' AND start_time IS NOT NULL AND complete_time IS NOT NULL 
        THEN TIMESTAMPDIFF(SECOND, start_time, complete_time) 
        ELSE NULL 
    END) as max_processing_time,
    MIN(CASE 
        WHEN task_status = 'COMPLETED' AND start_time IS NOT NULL AND complete_time IS NOT NULL 
        THEN TIMESTAMPDIFF(SECOND, start_time, complete_time) 
        ELSE NULL 
    END) as min_processing_time,
    SUM(CASE 
        WHEN task_status = 'COMPLETED' AND DATE(complete_time) = CURDATE() 
        THEN 1 ELSE 0 
    END) as today_processed_tasks,
    SUM(CASE 
        WHEN task_status = 'FAILED' AND DATE(update_time) = CURDATE() 
        THEN 1 ELSE 0 
    END) as today_failed_tasks,
    NOW() as statistics_time
FROM content_publish_task;
