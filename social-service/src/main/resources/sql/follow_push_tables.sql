-- 关注推送系统数据库表结构

-- 1. 关注推送配置表
CREATE TABLE `follow_push_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `push_type` tinyint(4) NOT NULL DEFAULT '3' COMMENT '推送类型：1-站内信推送，2-Feed流推送，3-两种都推送',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用推送：0-禁用，1-启用',
  `tenant_code` varchar(64) DEFAULT 'default' COMMENT '租户ID',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT 'system' COMMENT '创建者',
  `update_by` varchar(64) DEFAULT 'system' COMMENT '更新者',
  `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`, `deleted`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='关注推送配置表';

-- 2. 推送消息记录表
CREATE TABLE `push_message_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID（接收者）',
  `publisher_id` bigint(20) NOT NULL COMMENT '发布者ID',
  `content_id` bigint(20) NOT NULL COMMENT '内容ID',
  `push_type` tinyint(4) NOT NULL COMMENT '推送类型：1-站内信推送，2-Feed流推送',
  `push_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '推送状态：0-待推送，1-推送成功，2-推送失败，3-推送中，4-已取消',
  `message_title` varchar(255) DEFAULT NULL COMMENT '消息标题',
  `message_content` text COMMENT '消息内容',
  `push_time` datetime DEFAULT NULL COMMENT '推送时间',
  `fail_reason` varchar(500) DEFAULT NULL COMMENT '失败原因',
  `retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
  `tenant_code` varchar(64) NOT NULL  COMMENT '租户',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT 'system' COMMENT '创建者',
  `update_by` varchar(64) DEFAULT 'system' COMMENT '更新者',
  `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推送消息记录表';

-- 3. 用户Feed流表
CREATE TABLE `user_feed` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Feed ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `publisher_id` bigint(20) NOT NULL COMMENT '内容发布者ID',
  `content_id` bigint(20) NOT NULL COMMENT '内容ID',
  `publish_time` datetime NOT NULL COMMENT '发布时间',
  `read_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否已读：0-未读，1-已读',
  `tenant_code` varchar(64) DEFAULT 'default' COMMENT '租户ID',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT 'system' COMMENT '创建者',
  `update_by` varchar(64) DEFAULT 'system' COMMENT '更新者',
  `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_user_publish_time` (`user_id`, `publish_time` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户Feed流表';

-- 4. 内容发布事件表（用于记录和追踪）
CREATE TABLE `content_publish_event` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '事件ID',
  `event_id` varchar(64) NOT NULL COMMENT '事件唯一标识',
  `publisher_id` bigint(20) NOT NULL COMMENT '发布者ID',
  `content_id` bigint(20) NOT NULL COMMENT '内容ID',
  `process_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '处理状态：0-待处理，1-处理中，2-处理成功，3-处理失败',
  `process_time` datetime DEFAULT NULL COMMENT '处理时间',
  `fail_reason` varchar(500) DEFAULT NULL COMMENT '失败原因',
  `retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
  `tenant_code` varchar(64) DEFAULT 'default' COMMENT '租户ID',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_event_id` (`event_id`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='内容发布事件表';
