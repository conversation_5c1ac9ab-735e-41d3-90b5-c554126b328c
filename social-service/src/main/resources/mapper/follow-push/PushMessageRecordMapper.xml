<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chenbang.social.service.infrastructure.persistence.mapper.PushMessageRecordMapper">

    <resultMap id="BaseResultMap" type="com.chenbang.social.service.infrastructure.persistence.po.PushMessageRecordPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="publisher_id" property="publisherId" jdbcType="BIGINT"/>
        <result column="content_id" property="contentId" jdbcType="BIGINT"/>
        <result column="push_type" property="pushType" jdbcType="TINYINT"/>
        <result column="push_status" property="pushStatus" jdbcType="TINYINT"/>
        <result column="message_title" property="messageTitle" jdbcType="VARCHAR"/>
        <result column="message_content" property="messageContent" jdbcType="LONGVARCHAR"/>
        <result column="push_time" property="pushTime" jdbcType="TIMESTAMP"/>
        <result column="fail_reason" property="failReason" jdbcType="VARCHAR"/>
        <result column="retry_count" property="retryCount" jdbcType="INTEGER"/>
        <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="create_at" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_at" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, user_id, publisher_id, content_id, push_type, push_status, message_title, message_content,
        push_time, fail_reason, retry_count, tenant_code, create_at, update_at, create_by, update_by, deleted
    </sql>

    <insert id="insert" parameterType="com.chenbang.social.service.infrastructure.persistence.po.PushMessageRecordPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO push_message_record (
            user_id, publisher_id, content_id, push_type, push_status, message_title, message_content,
            push_time, fail_reason, retry_count, tenant_code, create_at, update_at, create_by, update_by, deleted
        ) VALUES (
            #{userId}, #{publisherId}, #{contentId}, #{pushType}, #{pushStatus}, #{messageTitle}, #{messageContent},
            #{pushTime}, #{failReason}, #{retryCount}, #{tenantCode}, #{createTime}, #{updateTime}, #{createBy}, #{updateBy}, #{deleted}
        )
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO push_message_record (
            user_id, publisher_id, content_id, push_type, push_status, message_title, message_content,
            push_time, fail_reason, retry_count, tenant_code, create_at, update_at, create_by, update_by, deleted
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (#{record.userId}, #{record.publisherId}, #{record.contentId}, #{record.pushType}, #{record.pushStatus}, 
             #{record.messageTitle}, #{record.messageContent}, #{record.pushTime}, #{record.failReason}, #{record.retryCount}, 
             #{record.tenantCode}, #{record.createTime}, #{record.updateTime}, #{record.createBy}, #{record.updateBy}, #{record.deleted})
        </foreach>
    </insert>

    <update id="update" parameterType="com.chenbang.social.service.infrastructure.persistence.po.PushMessageRecordPO">
        UPDATE push_message_record
        SET push_status = #{pushStatus},
            push_time = #{pushTime},
            fail_reason = #{failReason},
            retry_count = #{retryCount},
            update_at = #{updateTime},
            update_by = #{updateBy}
        WHERE id = #{id} AND deleted = 0
    </update>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM push_message_record
        WHERE id = #{id} AND deleted = 0
        LIMIT 1
    </select>

    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM push_message_record
        WHERE user_id = #{userId} AND deleted = 0
        ORDER BY create_at DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="selectFailedRecords" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM push_message_record
        WHERE push_status = 2 AND retry_count &lt; 3 AND deleted = 0
        ORDER BY create_at ASC
        LIMIT #{limit}
    </select>

    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE push_message_record
        SET deleted = 1, update_at = NOW()
        WHERE id = #{id} AND deleted = 0
    </update>

</mapper>
