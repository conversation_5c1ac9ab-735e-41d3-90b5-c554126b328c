<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chenbang.social.service.infrastructure.persistence.mapper.TaskBatchErrorMapper">

    <resultMap id="BaseResultMap" type="com.chenbang.social.service.infrastructure.persistence.po.TaskBatchErrorPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="VARCHAR"/>
        <result column="push_type" property="pushType" jdbcType="VARCHAR"/>
        <result column="batch_offset" property="batchOffset" jdbcType="INTEGER"/>
        <result column="error_message" property="errorMessage" jdbcType="LONGVARCHAR"/>
        <result column="retry_count" property="retryCount" jdbcType="INTEGER"/>
        <result column="is_fixed" property="isFixed" jdbcType="BOOLEAN"/>
        <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, task_id, push_type, batch_offset, error_message, retry_count, is_fixed,
        tenant_code, create_time, update_time, create_by, update_by
    </sql>

    <insert id="insert" parameterType="com.chenbang.social.service.infrastructure.persistence.po.TaskBatchErrorPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO task_batch_error (
            task_id, push_type, batch_offset, error_message, retry_count, is_fixed,
            tenant_code, create_by, update_by
        ) VALUES (
            #{taskId}, #{pushType}, #{batchOffset}, #{errorMessage}, #{retryCount}, #{isFixed},
            #{tenantCode}, #{createBy}, #{updateBy}
        )
    </insert>

    <update id="update" parameterType="com.chenbang.social.service.infrastructure.persistence.po.TaskBatchErrorPO">
        UPDATE task_batch_error SET
            retry_count = #{retryCount},
            is_fixed = #{isFixed},
            update_by = #{updateBy}
        WHERE id = #{id}
    </update>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM task_batch_error
        WHERE id = #{id}
    </select>

    <select id="selectByTaskId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM task_batch_error
        WHERE task_id = #{taskId}
        ORDER BY create_time DESC
    </select>

    <select id="selectUnfixedErrors" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM task_batch_error
        WHERE is_fixed = FALSE AND retry_count &lt; 3
        ORDER BY create_time ASC
        LIMIT #{limit}
    </select>

    <delete id="deleteExpiredErrors" parameterType="java.time.LocalDateTime">
        DELETE FROM task_batch_error
        WHERE create_time &lt; #{expireTime}
    </delete>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM task_batch_error
        WHERE id = #{id}
    </delete>

</mapper>
