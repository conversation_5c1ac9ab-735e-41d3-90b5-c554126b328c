<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chenbang.social.service.infrastructure.persistence.mapper.ContentPublishEventMapper">

    <resultMap id="BaseResultMap" type="com.chenbang.social.service.infrastructure.persistence.po.ContentPublishEventPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="event_id" property="eventId" jdbcType="VARCHAR"/>
        <result column="publisher_id" property="publisherId" jdbcType="BIGINT"/>
        <result column="content_id" property="contentId" jdbcType="BIGINT"/>
        <result column="content_type" property="contentType" jdbcType="TINYINT"/>
        <result column="content_title" property="contentTitle" jdbcType="VARCHAR"/>
        <result column="content_summary" property="contentSummary" jdbcType="VARCHAR"/>
        <result column="content_cover" property="contentCover" jdbcType="VARCHAR"/>
        <result column="content_url" property="contentUrl" jdbcType="VARCHAR"/>
        <result column="publish_time" property="publishTime" jdbcType="TIMESTAMP"/>
        <result column="process_status" property="processStatus" jdbcType="TINYINT"/>
        <result column="process_time" property="processTime" jdbcType="TIMESTAMP"/>
        <result column="fail_reason" property="failReason" jdbcType="VARCHAR"/>
        <result column="retry_count" property="retryCount" jdbcType="INTEGER"/>
        <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="create_at" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_at" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, event_id, publisher_id, content_id, content_type, content_title, content_summary, content_cover,
        content_url, publish_time, process_status, process_time, fail_reason, retry_count, tenant_code, create_at, update_at
    </sql>

    <insert id="insert" parameterType="com.chenbang.social.service.infrastructure.persistence.po.ContentPublishEventPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO content_publish_event (
            event_id, publisher_id, content_id, content_type, content_title, content_summary, content_cover,
            content_url, publish_time, process_status, process_time, fail_reason, retry_count, tenant_code, create_at, update_at
        ) VALUES (
            #{eventId}, #{publisherId}, #{contentId}, #{contentType}, #{contentTitle}, #{contentSummary}, #{contentCover},
            #{contentUrl}, #{publishTime}, #{processStatus}, #{processTime}, #{failReason}, #{retryCount}, #{tenantCode}, #{createTime}, #{updateTime}
        )
    </insert>

    <update id="update" parameterType="com.chenbang.social.service.infrastructure.persistence.po.ContentPublishEventPO">
        UPDATE content_publish_event
        SET process_status = #{processStatus},
            process_time = #{processTime},
            fail_reason = #{failReason},
            retry_count = #{retryCount},
            update_at = #{updateTime}
        WHERE id = #{id}
    </update>

    <select id="selectByEventId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM content_publish_event
        WHERE event_id = #{eventId}
        LIMIT 1
    </select>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM content_publish_event
        WHERE id = #{id}
        LIMIT 1
    </select>

    <select id="selectFailedEvents" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM content_publish_event
        WHERE process_status = 3 AND retry_count &lt; 3
        ORDER BY create_at ASC
        LIMIT #{limit}
    </select>

    <select id="selectByPublisherId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM content_publish_event
        WHERE publisher_id = #{publisherId}
        ORDER BY create_at DESC
        LIMIT #{offset}, #{limit}
    </select>

</mapper>
