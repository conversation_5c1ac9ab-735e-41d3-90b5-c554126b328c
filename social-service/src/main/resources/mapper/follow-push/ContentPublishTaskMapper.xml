<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chenbang.social.service.infrastructure.persistence.mapper.ContentPublishTaskMapper">

    <resultMap id="BaseResultMap" type="com.chenbang.social.service.infrastructure.persistence.po.ContentPublishTaskPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="VARCHAR"/>
        <result column="publisher_id" property="publisherId" jdbcType="BIGINT"/>
        <result column="content_id" property="contentId" jdbcType="BIGINT"/>
        <result column="content_type" property="contentType" jdbcType="TINYINT"/>
        <result column="content_title" property="contentTitle" jdbcType="VARCHAR"/>
        <result column="content_summary" property="contentSummary" jdbcType="LONGVARCHAR"/>
        <result column="content_cover" property="contentCover" jdbcType="VARCHAR"/>
        <result column="content_url" property="contentUrl" jdbcType="VARCHAR"/>
        <result column="task_status" property="taskStatus" jdbcType="VARCHAR"/>
        <result column="in_app_message_progress" property="inAppMessageProgress" jdbcType="INTEGER"/>
        <result column="feed_stream_progress" property="feedStreamProgress" jdbcType="INTEGER"/>
        <result column="error_message" property="errorMessage" jdbcType="LONGVARCHAR"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="complete_time" property="completeTime" jdbcType="TIMESTAMP"/>
        <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, task_id, publisher_id, content_id, content_type, content_title, content_summary,
        content_cover, content_url, task_status, in_app_message_progress, feed_stream_progress,
        error_message, start_time, complete_time, tenant_code, create_time, update_time,
        create_by, update_by
    </sql>

    <insert id="insert" parameterType="com.chenbang.social.service.infrastructure.persistence.po.ContentPublishTaskPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO content_publish_task (
            task_id, publisher_id, content_id, content_type, content_title, content_summary,
            content_cover, content_url, task_status, in_app_message_progress, feed_stream_progress,
            error_message, start_time, complete_time, tenant_code, create_by, update_by
        ) VALUES (
            #{taskId}, #{publisherId}, #{contentId}, #{contentType}, #{contentTitle}, #{contentSummary},
            #{contentCover}, #{contentUrl}, #{taskStatus}, #{inAppMessageProgress}, #{feedStreamProgress},
            #{errorMessage}, #{startTime}, #{completeTime}, #{tenantCode}, #{createBy}, #{updateBy}
        )
    </insert>

    <update id="update" parameterType="com.chenbang.social.service.infrastructure.persistence.po.ContentPublishTaskPO">
        UPDATE content_publish_task SET
            task_status = #{taskStatus},
            in_app_message_progress = #{inAppMessageProgress},
            feed_stream_progress = #{feedStreamProgress},
            error_message = #{errorMessage},
            start_time = #{startTime},
            complete_time = #{completeTime},
            update_by = #{updateBy}
        WHERE id = #{id}
    </update>

    <select id="selectByTaskId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM content_publish_task
        WHERE task_id = #{taskId}
    </select>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM content_publish_task
        WHERE id = #{id}
    </select>

    <select id="selectTasks" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM content_publish_task
        WHERE 1=1
        <if test="publisherId != null">
            AND publisher_id = #{publisherId}
        </if>
        <if test="contentId != null">
            AND content_id = #{contentId}
        </if>
        <if test="status != null and status != ''">
            AND task_status = #{status}
        </if>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countProcessingTasks" resultType="int">
        SELECT COUNT(*)
        FROM content_publish_task
        WHERE task_status = 'PROCESSING'
    </select>

    <select id="getTaskStatistics" resultType="map">
        SELECT * FROM v_task_statistics
    </select>

    <select id="getPublisherTaskStatistics" parameterType="java.lang.Long" resultType="map">
        SELECT 
            COUNT(*) as total_tasks,
            SUM(CASE WHEN task_status = 'PENDING' THEN 1 ELSE 0 END) as pending_tasks,
            SUM(CASE WHEN task_status = 'PROCESSING' THEN 1 ELSE 0 END) as processing_tasks,
            SUM(CASE WHEN task_status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_tasks,
            SUM(CASE WHEN task_status = 'FAILED' THEN 1 ELSE 0 END) as failed_tasks,
            SUM(CASE WHEN task_status = 'CANCELLED' THEN 1 ELSE 0 END) as cancelled_tasks,
            AVG(CASE 
                WHEN task_status = 'COMPLETED' AND start_time IS NOT NULL AND complete_time IS NOT NULL 
                THEN TIMESTAMPDIFF(SECOND, start_time, complete_time) 
                ELSE NULL 
            END) as avg_processing_time,
            MAX(CASE 
                WHEN task_status = 'COMPLETED' AND start_time IS NOT NULL AND complete_time IS NOT NULL 
                THEN TIMESTAMPDIFF(SECOND, start_time, complete_time) 
                ELSE NULL 
            END) as max_processing_time,
            MIN(CASE 
                WHEN task_status = 'COMPLETED' AND start_time IS NOT NULL AND complete_time IS NOT NULL 
                THEN TIMESTAMPDIFF(SECOND, start_time, complete_time) 
                ELSE NULL 
            END) as min_processing_time,
            SUM(CASE 
                WHEN task_status = 'COMPLETED' AND DATE(complete_time) = CURDATE() 
                THEN 1 ELSE 0 
            END) as today_processed_tasks,
            SUM(CASE 
                WHEN task_status = 'FAILED' AND DATE(update_time) = CURDATE() 
                THEN 1 ELSE 0 
            END) as today_failed_tasks,
            NOW() as statistics_time
        FROM content_publish_task
        WHERE publisher_id = #{publisherId}
    </select>

    <delete id="deleteExpiredTasks" parameterType="java.time.LocalDateTime">
        DELETE FROM content_publish_task
        WHERE create_time &lt; #{expireTime}
        AND task_status IN ('COMPLETED', 'FAILED', 'CANCELLED')
    </delete>

</mapper>
