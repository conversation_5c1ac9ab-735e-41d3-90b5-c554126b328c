<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chenbang.social.service.infrastructure.persistence.mapper.FollowPushConfigMapper">

    <resultMap id="BaseResultMap" type="com.chenbang.social.service.infrastructure.persistence.po.FollowPushConfigPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="push_type" property="pushType" jdbcType="TINYINT"/>
        <result column="enabled" property="enabled" jdbcType="BOOLEAN"/>
        <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="create_at" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_at" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, user_id, push_type, enabled, tenant_code, create_at, update_at, create_by, update_by, deleted
    </sql>

    <insert id="insert" parameterType="com.chenbang.social.service.infrastructure.persistence.po.FollowPushConfigPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO follow_push_config (
            user_id, push_type, enabled, tenant_code, create_at, update_at, create_by, update_by, deleted
        ) VALUES (
            #{userId}, #{pushType}, #{enabled}, #{tenantCode}, #{createTime}, #{updateTime}, #{createBy}, #{updateBy}, #{deleted}
        )
    </insert>

    <update id="update" parameterType="com.chenbang.social.service.infrastructure.persistence.po.FollowPushConfigPO">
        UPDATE follow_push_config
        SET push_type = #{pushType},
            enabled = #{enabled},
            update_at = #{updateTime},
            update_by = #{updateBy}
        WHERE id = #{id} AND deleted = 0
    </update>

    <select id="selectByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM follow_push_config
        WHERE user_id = #{userId} AND deleted = 0
        LIMIT 1
    </select>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM follow_push_config
        WHERE id = #{id} AND deleted = 0
        LIMIT 1
    </select>

    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE follow_push_config
        SET deleted = 1, update_at = NOW()
        WHERE id = #{id} AND deleted = 0
    </update>

</mapper>
