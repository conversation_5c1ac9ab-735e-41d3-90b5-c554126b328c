<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chenbang.social.service.infrastructure.persistence.mapper.UserFeedMapper">

    <resultMap id="BaseResultMap" type="com.chenbang.social.service.infrastructure.persistence.po.UserFeedPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="publisher_id" property="publisherId" jdbcType="BIGINT"/>
        <result column="content_id" property="contentId" jdbcType="BIGINT"/>
        <result column="content_type" property="contentType" jdbcType="TINYINT"/>
        <result column="content_title" property="contentTitle" jdbcType="VARCHAR"/>
        <result column="content_summary" property="contentSummary" jdbcType="VARCHAR"/>
        <result column="content_cover" property="contentCover" jdbcType="VARCHAR"/>
        <result column="content_url" property="contentUrl" jdbcType="VARCHAR"/>
        <result column="publish_time" property="publishTime" jdbcType="TIMESTAMP"/>
        <result column="read_status" property="readStatus" jdbcType="TINYINT"/>
        <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="create_at" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_at" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, user_id, publisher_id, content_id, content_type, content_title, content_summary, content_cover,
        content_url, publish_time, read_status, tenant_code, create_at, update_at, create_by, update_by, deleted
    </sql>

    <insert id="insert" parameterType="com.chenbang.social.service.infrastructure.persistence.po.UserFeedPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_feed (
            user_id, publisher_id, content_id, content_type, content_title, content_summary, content_cover,
            content_url, publish_time, read_status, tenant_code, create_at, update_at, create_by, update_by, deleted
        ) VALUES (
            #{userId}, #{publisherId}, #{contentId}, #{contentType}, #{contentTitle}, #{contentSummary}, #{contentCover},
            #{contentUrl}, #{publishTime}, #{readStatus}, #{tenantCode}, #{createTime}, #{updateTime}, #{createBy}, #{updateBy}, #{deleted}
        )
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO user_feed (
            user_id, publisher_id, content_id, content_type, content_title, content_summary, content_cover,
            content_url, publish_time, read_status, tenant_code, create_at, update_at, create_by, update_by, deleted
        ) VALUES
        <foreach collection="feeds" item="feed" separator=",">
            (#{feed.userId}, #{feed.publisherId}, #{feed.contentId}, #{feed.contentType}, #{feed.contentTitle}, 
             #{feed.contentSummary}, #{feed.contentCover}, #{feed.contentUrl}, #{feed.publishTime}, #{feed.readStatus}, 
             #{feed.tenantCode}, #{feed.createTime}, #{feed.updateTime}, #{feed.createBy}, #{feed.updateBy}, #{feed.deleted})
        </foreach>
    </insert>

    <update id="update" parameterType="com.chenbang.social.service.infrastructure.persistence.po.UserFeedPO">
        UPDATE user_feed
        SET read_status = #{readStatus},
            update_at = #{updateTime},
            update_by = #{updateBy}
        WHERE id = #{id} AND deleted = 0
    </update>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_feed
        WHERE id = #{id} AND deleted = 0
        LIMIT 1
    </select>

    <select id="selectByUserIdWithTimeline" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_feed
        WHERE user_id = #{userId} AND deleted = 0
        <if test="lastTimestamp != null">
            AND UNIX_TIMESTAMP(publish_time) * 1000 &lt; #{lastTimestamp}
        </if>
        <if test="contentType != null">
            AND content_type = #{contentType}
        </if>
        ORDER BY publish_time DESC
        LIMIT #{limit}
    </select>

    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_feed
        WHERE user_id = #{userId} AND deleted = 0
        <if test="contentType != null">
            AND content_type = #{contentType}
        </if>
        ORDER BY publish_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <delete id="deleteExpiredFeeds">
        DELETE FROM user_feed
        WHERE create_at &lt; #{expireTime} AND deleted = 0
    </delete>

    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE user_feed
        SET deleted = 1, update_at = NOW()
        WHERE id = #{id} AND deleted = 0
    </update>

</mapper>
